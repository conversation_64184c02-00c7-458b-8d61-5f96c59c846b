{"name": "vue-ticket-management-system", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@vicons/ionicons5": "^0.12.0", "axios": "^1.6.2", "date-fns": "^2.30.0", "echarts": "^5.4.3", "marked": "^12.0.0", "naive-ui": "^2.34.4", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.3", "vue": "^3.4.38", "vue-echarts": "^6.6.1", "vue-router": "^4.2.5"}, "devDependencies": {"@types/node": "^20.0.0", "@vitejs/plugin-vue": "^5.1.3", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.5.3", "vite": "^5.4.2", "vue-tsc": "^2.1.4"}}