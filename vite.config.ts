// vite.config.ts
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import path from 'path';

export default defineConfig({
  base: '/iticket/',
  plugins: [vue()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
    },
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: undefined,
      },
    },
    modulePreload: false,
    assetsDir: 'assets',
  },
  server: {
    port: 5173,
    host: true,
    proxy: {
      '/api': {
        target: 'https://ci.labtools.china.nsn-net.net', // 修正：去除末尾空格
        changeOrigin: true,
        secure: false, // 保持false以处理SSL证书问题
        // 重写路径（如果后端API不需要/iticket/api前缀）
        // rewrite: (path) => path.replace(/^\/iticket\/api/, '/api'),
        configure: (proxy) => {
          // 处理代理请求
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('代理请求:', req.method, req.url);
            
            // 处理OPTIONS预检请求
            if (req.method === 'OPTIONS') {
              // 阻止请求继续转发
              proxyReq.destroy();

              // 直接响应预检请求
              if (!res.headersSent) {
                res.writeHead(204, {
                  'Access-Control-Allow-Origin': '*',
                  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS, HEAD, PATCH',
                  'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, Accept, Origin, Access-Control-Request-Method, Access-Control-Request-Headers',
                  'Access-Control-Allow-Credentials': 'true',
                  'Access-Control-Max-Age': '86400',
                  'Content-Length': '0'
                });
                res.end();
              }
              return;
            }
            
            // 设置必要的请求头（非OPTIONS请求）
            proxyReq.setHeader('Origin', 'https://ci.labtools.china.nsn-net.net');
            proxyReq.setHeader('Referer', 'https://ci.labtools.china.nsn-net.net');
            proxyReq.setHeader('X-Forwarded-Proto', 'https');
            
            // 安全地设置host头
            const host = req.headers.host;
            if (host) {
              proxyReq.setHeader('X-Forwarded-Host', host);
            }
            
            // 如果需要认证token，可以在这里添加
            // proxyReq.setHeader('Authorization', 'Bearer your-token');
          });

          // 处理代理响应
          proxy.on('proxyRes', (proxyRes, req, res) => {
            console.log('代理响应:', proxyRes.statusCode, req.url);
            
            // 删除后端原有的CORS头，避免冲突
            const corsHeaders = [
              'access-control-allow-origin',
              'access-control-allow-methods',
              'access-control-allow-headers',
              'access-control-allow-credentials',
              'access-control-expose-headers',
              'access-control-max-age'
            ];
            
            corsHeaders.forEach(header => {
              if (proxyRes.headers[header]) {
                delete proxyRes.headers[header];
              }
            });

            // 设置新的CORS响应头
            res.setHeader('Access-Control-Allow-Origin', '*');
            res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, HEAD, PATCH');
            res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept, Origin, Access-Control-Request-Method, Access-Control-Request-Headers');
            res.setHeader('Access-Control-Allow-Credentials', 'true');
            res.setHeader('Access-Control-Expose-Headers', '*');
            res.setHeader('Access-Control-Max-Age', '86400');
          });



          // 错误处理
          proxy.on('error', (err, _req, res) => {
            console.error('代理错误:', err);
            if (!res.headersSent) {
              res.writeHead(500, { 'Content-Type': 'text/plain' });
              res.end('代理服务器错误: ' + err.message);
            }
          });
        },
      },
    },
  },
});