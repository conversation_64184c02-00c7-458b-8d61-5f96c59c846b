#!/bin/bash

# 开发环境启动脚本
set -e

echo "🚀 启动开发环境..."

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

# 检查Docker Compose是否安装
if ! command -v docker compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 停止现有的开发容器
echo "🛑 停止现有的开发容器..."
docker compose -f docker compose.dev.yml down

# 构建开发镜像
echo "🔨 构建开发镜像..."
docker compose -f docker compose.dev.yml build

# 启动开发环境
echo "🚀 启动开发环境..."
docker compose -f docker compose.dev.yml up -d

# 显示日志
echo "📋 显示服务日志..."
docker compose -f docker compose.dev.yml logs -f
