#!/bin/bash

# 构建脚本
set -e

echo "🚀 开始构建Vue票务管理系统..."

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

# 检查Docker Compose是否安装
if ! command -v docker compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 清理旧的镜像和容器
echo "🧹 清理旧的容器和镜像..."
docker compose down --remove-orphans
docker system prune -f

# 构建生产镜像
echo "🔨 构建生产镜像..."
docker compose build --no-cache

# 启动服务
echo "🚀 启动服务..."
docker compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
docker compose ps

# 健康检查
echo "🏥 执行健康检查..."
if curl -f http://localhost/health > /dev/null 2>&1; then
    echo "✅ 服务启动成功！"
    echo "🌐 访问地址: http://localhost/iticket/"
else
    echo "❌ 服务启动失败，请检查日志"
    docker compose logs
    exit 1
fi
