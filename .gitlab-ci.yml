variables:
  REGISTRY: "mn-hzlab-local.artifactory-hz1.int.net.nokia.com"

stages:
  - check-env
  - build-image
  - push-image
  - deploy

before_script:
  - 'which ssh-agent || ( apt-get update -qy && apt-get install openssh-client -qqy )'
  - eval `ssh-agent -s`
  - echo "$SSH_ID_RSA" | tr -d '\r' | ssh-add -
  - mkdir -p ~/.ssh
  - chmod 700 ~/.ssh/
  - echo "$SSH_ID_RSA" >> ~/.ssh/id_rsa
  - chmod 700 ~/.ssh/id_rsa
  - ssh-keyscan gitlabe2.ext.net.nokia.com >> ~/.ssh/known_hosts
  - chmod 644 ~/.ssh/known_hosts
  - ssh-keyscan -H *********** >> ~/.ssh/known_hosts
  - docker info


check-env:
  stage: check-env
  tags:
    - docker-runner
  script:
    - changed=$(git diff-tree --no-commit-id --name-only -r HEAD^ HEAD | grep -q "\.env$" && echo "true" || echo "false")  
    - |
      if [ "$changed" == "true" ]; then  
        echo ".env file has been changed in this commit.";
        TAG=$(grep -oE '^TAG=([0-9]+\.([0-9]+(\.[0-9]+)*))' .env | cut -d= -f2);
        echo "The TAG value is:$TAG";
        echo "export change=0" >> tag.sh;
      else
        echo ".env file has not been changed in this commit.";
        OLD_TAG=$(grep -oE '^TAG=([0-9]+\.([0-9]+(\.[0-9]+)*))' .env | cut -d= -f2);
        echo "The TAG:$OLD_TAG";
        TAG=$(echo "$OLD_TAG" | awk -F'.' '{printf "%s.%s.%d\n", $1, $2, $3+1}');
        echo "New TAG:$TAG";
        awk -v tag="$OLD_TAG" -v new_tag="TAG=$TAG  # Automatic update version $CURRENT_DATETIME" '$0 ~ "^TAG="tag {print; print new_tag; next} {print}' .env > .env.tmp && mv .env.tmp .env;
        awk -v tag="$OLD_TAG" 'index($0, "TAG=" tag) == 1 { print "# " $0; next } { print }' .env > .env.tmp && mv .env.tmp .env;
        echo "export change=1" >> tag.sh;
      fi
    - echo "export TAG=$TAG" >> tag.sh
  artifacts:  
    paths:  
      - tag.sh
      - .env


build-docker-image:
  stage: build-image
  tags:
    - docker-runner
  needs: ["check-env"]
  script:
    - echo "starting to build app images..."
    - docker compose build --parallel
  dependencies:
    - check-env

push-docker-image:
  stage: push-image
  tags:
    - docker-runner
  needs:
    - ["check-env", "build-docker-image"]
  retry: 2
  script:
    - BRANCH_NAME="master";
    - branch=$(git branch --list | grep -q "^  $BRANCH_NAME$" && echo "true" || echo "false");
    - |
      if [ "$branch" == "true" ]; then 
        # 分支存在
        echo "Branch $BRANCH_NAME exists. Switching to it.";  
        git branch -D $BRANCH_NAME;
      fi
    - git checkout -b $BRANCH_NAME;  
    - source tag.sh
    - rm tag.sh
    - echo "starting push iticket images..."
    - docker login -u $ARTIFACTORY_USER -p $ARTIFACTORY_TOKEN $REGISTRY
    - docker compose push
    - docker logout
    - git config user.email $CI_EMAIL
    - git config user.name $CI_USERNAME
    - git remote set-<NAME_EMAIL>:hzlab/iticket.git;
    - |
      if [ "$change" == 1 ]; then 
        git add .env;
        git commit -m "automated update version [skip ci]";
        git push origin $BRANCH_NAME -o ci.skip;
      fi
    - git tag -f "$TAG";
    - git push -f origin "$TAG" -o ci.skip;
  dependencies:  
    - check-env


deploy-to-ci:
  stage: deploy
  tags:
    - docker-runner
  needs: ["check-env", "build-docker-image", "push-docker-image"]
  script:
    - echo "starting to deploy images..."
    - whoami
    - hostname
    - sh run.sh
    - echo "deploy images done"
  when: manual
  dependencies:
    - check-env
