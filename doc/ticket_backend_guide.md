# Ticket 系统后端开发指南

## 概述

本文档为后端开发人员提供开发符合当前前端接口的 Ticket 系统后端 API 的详细指南。涵盖 Ticket 的创建、查询操作以及 Worklog 的添加操作。

## 数据模型

### Ticket 数据结构

```typescript
interface Ticket {
  // 基础标识字段
  issues_id: string;           // 主键，唯一标识符
  issues_key: string;          // 显示用的票据编号，如 "TIC-101"
  
  // 基本信息
  summary: string;             // 票据标题（必填）
  description: string;         // 详细描述
  priority: 'Minor' | 'Low' | 'Medium' | 'High' | 'Critical';
  status: 'Open' | 'In Progress' | 'Resolved' | 'Closed';
  
  // 服务相关
  service_package: string;     // 服务包类型
  component?: string;          // 位置信息（两级级联选择）
  
  // 人员信息
  assignee_email: string;      // 分配人邮箱
  reporter_email: string;      // 报告人邮箱
  assignee?: string;           // 分配人姓名（显示用）
  reporter?: string;           // 报告人姓名（显示用）
  
  // 时间字段
  created: string;             // 创建时间 (ISO 8601)
  updated: string;             // 更新时间 (ISO 8601)
  resolved?: string;           // 解决时间
  
  // 工作日志
  worklogs?: Worklog[];        // 一对多关系的工作日志
  
  // 其他字段（可选）
  ordering_business_line?: string;
  lab_location?: string;
  issue_type?: string;
  resolution?: string;
  time_to_resolution?: string;
  feedback?: string;
  watchers?: string;
  lsdsup_remaining_time?: string;
  yes_sla_missed?: number;
  catalog?: string;
  filtered_desc?: string;
  ai_decision_logs?: string;
  human_feedback_annotations?: string;
}
```

### Worklog 数据结构

```typescript
interface Worklog {
  id: number;                  // 主键
  worklog_author?: string;     // 工作日志作者邮箱
  worklog_time?: string;       // 记录时间 (ISO 8601)
  worklog_timeSpent?: number;  // 耗时（小时）
  worklog_comment?: string;    // 工作备注
  ticket_id: string;          // 关联的 issues_id
}
```

## API 接口规范

### 1. 获取用户相关票据

**端点**: `GET /api/tickets`

**描述**: 获取指定用户创建或分配的票据

**查询参数**:
- `userEmail`: 用户邮箱地址（必填）
- `duration`: 获取最近多少天的数据，默认30天（可选）

**请求示例**:
```
GET /api/tickets?userEmail=<EMAIL>&duration=30
GET /api/tickets?userEmail=<EMAIL>  // 默认30天
```

**响应格式**:
```typescript
// 直接返回 Ticket 数组
Ticket[]
```

**实现要点**:
- 返回该用户作为 `reporter_email` 或 `assignee_email` 的票据
- 根据 `duration` 参数过滤最近N天的数据（基于 `updated` 字段）
- 前端会根据邮箱字段自动过滤显示
- 包含完整的 worklogs 数组数据

**SQL 查询示例**:
```sql
SELECT t.*, w.* FROM tickets t
LEFT JOIN worklogs w ON t.issues_id = w.ticket_id
WHERE (t.reporter_email = ? OR t.assignee_email = ?)
  AND t.updated >= DATE_SUB(NOW(), INTERVAL ? DAY)
ORDER BY t.updated DESC;
```

### 2. 创建新Ticket

**端点**: `POST /api/tickets`

**描述**: 创建新的Ticket

**请求体**:
```typescript
Partial<Ticket> // 包含必要字段的票据数据
```

**必填字段**:
- `summary`: 票据标题
- `description`: 详细描述
- `service_package`: 服务包类型
- `reporter_email`: 报告人邮箱

**响应格式**:
```typescript
Ticket // 完整的新创建票据对象
```

**实现要点**:
- 自动生成 `issues_id`（建议使用 UUID 或时间戳）
- jira生成 `issues_key`
- 设置默认值：
  - `status`: "Open"
  - `priority`: "Medium"（如未指定）
  - `created`: 当前时间
  - `updated`: 当前时间
  - `worklogs`: 空数组
- 验证必填字段
- 返回完整的票据对象
- 需要先在jira上创建ticket, 成功之后才有JIRA相关的数据

### 3. 添加工作日志

**端点**: `POST /api/tickets/:ticketId/worklogs`

**描述**: 为指定票据添加工作日志

**路径参数**:
- `ticketId`: 票据的 issues_id

**请求体**:
```typescript
Partial<Worklog> // 工作日志数据
```

**必填字段**:
- `worklog_timeSpent`: 耗时（必须 > 0）
- `worklog_author`: 作者邮箱
- `worklog_time`: 记录时间

**响应格式**:
```typescript
Ticket // 更新后的完整票据对象（包含新的 worklog）
```

**实现要点**:
- 验证票据存在性
- 验证 `worklog_timeSpent > 0`
- 自动生成 worklog 的 `id`
- 更新票据的 `updated` 时间
- 返回包含所有 worklogs 的完整票据对象



## 认证和授权

### 请求头处理
```javascript
// 中间件：验证 JWT token
app.use('/api', (req, res, next) => {
  const token = req.headers.authorization?.replace('Bearer ', '');
  if (!token) {
    return res.status(401).json({ error: 'No token provided' });
  }
  
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = decoded;
    next();
  } catch (error) {
    res.status(401).json({ error: 'Invalid token' });
  }
});
```

