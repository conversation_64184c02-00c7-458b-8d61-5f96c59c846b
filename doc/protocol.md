# Chatbot 通信协议开发者文档

## 设计背景

### 核心需求
本系统需要支持前端与后端之间的双向通信，除了传统的用户-机器人对话外，还需要支持命令形式的消息交互。前端可以发送各种命令给后端（如设备查询、虚拟机重置、工单预分配等），后端需要能够区分处理不同类型的请求并返回相应的结果。

### 设计原则
1. **职责分离**：明确区分通信协议（前后端交互）和UI状态模型（前端内部状态）
2. **类型安全**：使用TypeScript确保消息格式的类型安全
3. **可扩展性**：支持未来添加新的命令类型
4. **统一架构**：聊天和命令使用相同的SSE通信机制
5. **实时性**：使用Server-Sent Events (SSE) 实现流式响应

### 架构概览
```
前端应用 ←→ 通信协议 ←→ 后端服务
   ↓           ↓           ↓
UI状态模型   SSE消息格式   命令处理器
```

## 协议格式

### 命令输出模式

系统支持两种命令输出模式：

#### 流式输出模式
适用于需要实时反馈执行过程的命令（如设备查询、系统诊断等）：
- 使用 `token_chunk` 事件流式推送执行日志
- 前端创建 `assistant` 类型消息进行实时内容累积
- 提供丰富的视觉反馈和进度指示

#### 非流式输出模式
适用于快速执行的命令（如简单操作、状态切换等）：
- 直接返回 `command_receipt` 事件
- 前端创建 `system` 类型消息显示执行结果
- 简洁的成功/失败状态反馈

### 客户端消息格式 (ClientMessage)

#### 基础消息接口
```typescript
interface BaseMessage {
  messageId: string; // 客户端生成的唯一ID，用于端到端追踪
}
```

#### 聊天消息
```typescript
interface ChatClientMessage extends BaseMessage {
  type: 'chat';
  payload: { 
    content: string; 
  };
}
```

#### 命令消息
```typescript
interface CommandClientMessage<T extends string> extends BaseMessage {
  type: 'command';
  command: T;
  payload: T extends 'query_device' ? QueryDevicePayload :
           T extends 'vm_reset' ? VmResetPayload :
           T extends 'ticket_preassign' ? TicketPreassignPayload :
           never;
}
```

#### 命令载荷类型
```typescript
// 设备查询
interface QueryDevicePayload { 
  device_sn: string; 
}

// 虚拟机重置
interface VmResetPayload { 
  vm_id: string; 
  force_shutdown?: boolean; 
}

// 工单预分配
interface TicketPreassignPayload { 
  // 请参考 Ticket.ts定义
}
```

#### 完整的客户端消息联合类型
```typescript
export type ClientMessage =
  | ChatClientMessage
  | CommandClientMessage<'query_device'>
  | CommandClientMessage<'vm_reset'>
  | CommandClientMessage<'ticket_preassign'>;
```

### 服务端事件格式 (ServerEvent)

#### 标准化事件外壳
```typescript
interface ServerEvent<T = any> {
  event: 'token_chunk' | 'command_receipt' | 'error' | 'stream_end';
  data: T;
  requestId: string; // 回传客户端的messageId
  timestamp: number;
}
```

#### 具体事件数据类型
```typescript
// 流式文本块
interface TokenChunkData { 
  content: string; 
}

// 命令执行结果
interface CommandReceiptData {
  command: string;
  status: 'success' | 'failure';
  message: string;
  result?: any; // 可选的命令执行结果
}

// 错误信息
interface ErrorData { 
  code: number; 
  message: string;
  retryable?: boolean;
  context?: any;
}

// 流结束
interface StreamEndData { 
  reason: 'completed' | 'stopped' | 'error'; 
}
```

## 数据流

### 聊天消息流程
```
1. 用户输入 → 前端创建ChatClientMessage
2. 前端通过POST /api/chat发送消息
3. 后端建立SSE连接
4. 后端流式返回token_chunk事件
5. 前端实时更新UI显示
6. 后端发送stream_end事件结束
```

### 命令消息流程

#### 流式命令流程（如 query_device）
```
1. 用户点击命令按钮 → 前端创建CommandClientMessage
2. 前端创建assistant类型消息，设置isStreaming=true
3. 前端通过POST /api/chat发送命令
4. 后端识别为流式命令，开始执行
5. 后端发送多个token_chunk事件推送执行日志
6. 前端onTokenChunk处理器累积内容到assistant消息
7. 后端发送command_receipt事件（可选，用于状态更新）
8. 后端发送stream_end事件结束
```

#### 非流式命令流程（如 vm_reset）
```
1. 用户点击命令按钮 → 前端创建CommandClientMessage
2. 前端创建system类型消息，显示执行状态
3. 前端通过POST /api/chat发送命令
4. 后端识别为非流式命令，执行完成后
5. 后端发送command_receipt事件返回结果
6. 后端发送stream_end事件结束
```

### SSE事件序列示例

#### 聊天响应
```
event: token_chunk
data: {"event":"token_chunk","data":{"content":"Hello"},"requestId":"msg-123","timestamp":1678886400000}

event: token_chunk  
data: {"event":"token_chunk","data":{"content":" World"},"requestId":"msg-123","timestamp":1678886401000}

event: stream_end
data: {"event":"stream_end","data":{"reason":"completed"},"requestId":"msg-123","timestamp":1678886402000}
```

#### 流式命令响应（query_device）
```
event: token_chunk
data: {"event":"token_chunk","data":{"content":"正在连接到设备管理系统...\n"},"requestId":"cmd-456","timestamp":1678886400000}

event: token_chunk
data: {"event":"token_chunk","data":{"content":"验证设备序列号...\n"},"requestId":"cmd-456","timestamp":1678886400500}

event: token_chunk
data: {"event":"token_chunk","data":{"content":"查询设备基本信息...\n"},"requestId":"cmd-456","timestamp":1678886401000}

event: token_chunk
data: {"event":"token_chunk","data":{"content":"查询完成！\n\n📋 设备信息详情：\n• 设备序列号: PD31334\n• 设备型号: Dell OptiPlex 7090\n• 运行状态: 🟢 Active\n"},"requestId":"cmd-456","timestamp":1678886401500}

event: command_receipt
data: {"event":"command_receipt","data":{"command":"query_device","status":"success","message":"Device information retrieved successfully"},"requestId":"cmd-456","timestamp":1678886402000}

event: stream_end
data: {"event":"stream_end","data":{"reason":"completed"},"requestId":"cmd-456","timestamp":1678886402100}
```

#### 非流式命令响应（vm_reset）
```
event: command_receipt
data: {"event":"command_receipt","data":{"command":"vm_reset","status":"success","message":"Virtual machine vm-123 has been reset successfully","result":{"vm_id":"vm-123","status":"Running","reset_time":"2024-01-15T10:30:00Z"}},"requestId":"cmd-789","timestamp":1678886400000}

event: stream_end
data: {"event":"stream_end","data":{"reason":"completed"},"requestId":"cmd-789","timestamp":1678886400100}
```

## 前端API规范

### ChatSSEService 类

#### 发送消息
```typescript
static async sendMessage(
  message: ClientMessage,
  handlers: SSEEventHandlers,
  options: SSEOptions = {}
): Promise<void>
```

**注意**: 生产环境下使用统一的 `sseRequest` 工具，确保与其他API调用保持一致的认证、错误处理等。

#### 事件处理器接口
```typescript
interface SSEEventHandlers {
  onTokenChunk?: (data: TokenChunkData, event: ServerEvent<TokenChunkData>) => void;
  onCommandReceipt?: (data: CommandReceiptData, event: ServerEvent<CommandReceiptData>) => void;
  onError?: (data: ErrorData, event: ServerEvent<ErrorData>) => void;
  onStreamEnd?: (data: StreamEndData, event: ServerEvent<StreamEndData>) => void;
}
```

#### 连接选项
```typescript
interface SSEOptions {
  abortSignal?: AbortSignal;
  timeout?: number;
}
```

### ChatStore 状态管理

#### 发送聊天消息
```typescript
const sendMessage = async (content: string) => Promise<void>
```

#### 发送命令
```typescript
const sendCommand = async (
  command: string, 
  payload: any, 
  displayMessage?: string
) => Promise<void>
```

### UI状态模型 (ChatMessage)

```typescript
interface ChatMessage {
  id: string;
  content: string;
  type: 'user' | 'assistant' | 'system';
  timestamp: Date;
  status?: 'loading' | 'error' | 'done' | 'cancelled';
  metadata?: {
    commandType?: string;
    requestId?: string;
    isStreaming?: boolean;  // 标识是否为流式命令
  };
}
```

### 流式命令配置

#### 前端配置
在 `chat.ts` 中配置哪些命令使用流式输出：

```typescript
// 流式命令列表
const isStreamingCommand = ['query_device'].includes(command);

// 根据命令类型创建不同的消息
const commandMessage: ChatMessage = {
  id: Date.now().toString(),
  content: isStreamingCommand ? '正在查询设备信息...\n' : displayMessage,
  type: isStreamingCommand ? 'assistant' : 'system',  // 关键区别
  timestamp: new Date(),
  status: 'loading',
  metadata: {
    commandType: command,
    requestId: Date.now().toString(),
    isStreaming: isStreamingCommand
  }
};
```

#### 事件处理器配置
```typescript
const handlers: SSEEventHandlers = {
  onTokenChunk: (data) => {
    // 只有流式命令才处理 token_chunk
    if (commandMessage.metadata?.isStreaming) {
      commandMessage.content += data.content;
      messages.value = [...messages.value];
    }
  },
  onCommandReceipt: (data) => {
    if (commandMessage.metadata?.isStreaming) {
      // 流式命令：只更新状态，不修改内容
      commandMessage.status = data.status === 'success' ? 'done' : 'error';
    } else {
      // 非流式命令：更新内容和状态
      commandMessage.content = `[命令${data.status === 'success' ? '成功' : '失败'}: ${data.message}]`;
      commandMessage.status = data.status === 'success' ? 'done' : 'error';
    }
  }
};
```

## 后端API规范

### 端点规范

#### POST /api/chat
- **Content-Type**: `application/json`
- **Accept**: `text/event-stream`
- **请求体**: `ClientMessage`
- **响应**: SSE流

### 请求处理流程

#### 1. 消息解析
```typescript
// 伪代码
const message: ClientMessage = JSON.parse(requestBody);
```

#### 2. 类型路由
```typescript
if (message.type === 'chat') {
  // 路由到聊天处理器
  await handleChatMessage(message, sseStream);
} else if (message.type === 'command') {
  // 路由到命令处理器，根据命令类型选择处理方式
  await handleCommandMessage(message, sseStream);
}
```

#### 3. 命令处理分发
```typescript
async function handleCommandMessage(message: CommandClientMessage, sseStream: SSEStream) {
  const { command, payload } = message;

  // 判断是否为流式命令
  const streamingCommands = ['query_device'];
  const isStreaming = streamingCommands.includes(command);

  if (isStreaming) {
    // 流式命令处理
    await handleStreamingCommand(command, payload, message.messageId, sseStream);
  } else {
    // 非流式命令处理
    await handleNonStreamingCommand(command, payload, message.messageId, sseStream);
  }
}

// 流式命令处理示例
async function handleStreamingCommand(command: string, payload: any, requestId: string, sseStream: SSEStream) {
  if (command === 'query_device') {
    // 发送执行步骤日志
    const steps = [
      '正在连接到设备管理系统...\n',
      '验证设备序列号...\n',
      '查询设备基本信息...\n',
      '获取设备状态信息...\n',
      '查询完成！\n\n📋 设备信息详情：\n...'
    ];

    for (const step of steps) {
      // 发送 token_chunk 事件
      sseStream.sendEvent('token_chunk', { content: step }, requestId);
      await delay(300); // 模拟处理时间
    }

    // 发送最终结果
    sseStream.sendEvent('command_receipt', {
      command,
      status: 'success',
      message: 'Device information retrieved successfully'
    }, requestId);
  }

  // 结束流
  sseStream.sendEvent('stream_end', { reason: 'completed' }, requestId);
}

// 非流式命令处理示例
async function handleNonStreamingCommand(command: string, payload: any, requestId: string, sseStream: SSEStream) {
  // 执行命令逻辑
  const result = await executeCommand(command, payload);

  // 直接发送结果
  sseStream.sendEvent('command_receipt', {
    command,
    status: result.success ? 'success' : 'failure',
    message: result.message,
    result: result.data
  }, requestId);

  // 结束流
  sseStream.sendEvent('stream_end', { reason: 'completed' }, requestId);
}
```

#### 3. SSE响应格式
```typescript
// 发送具名事件
response.write(`event: ${eventType}\n`);
response.write(`data: ${JSON.stringify(serverEvent)}\n\n`);
```

### 命令处理器接口

```typescript
interface CommandHandler {
  execute(payload: any): Promise<CommandReceiptData>;
  validate?(payload: any): boolean;
}
```

### 错误处理

#### 客户端错误 (4xx)
```json
{
  "event": "error",
  "data": {
    "code": 400,
    "message": "Invalid message format",
    "retryable": false
  },
  "requestId": "msg-123",
  "timestamp": 1678886400000
}
```

#### 服务端错误 (5xx)
```json
{
  "event": "error", 
  "data": {
    "code": 500,
    "message": "Internal server error",
    "retryable": true
  },
  "requestId": "msg-123",
  "timestamp": 1678886400000
}
```

## 开发指南

### 添加新命令类型

#### 1. 定义载荷类型
```typescript
// src/types/protocol.ts
interface NewCommandPayload {
  param1: string;
  param2?: number;
}
```

#### 2. 更新命令联合类型
```typescript
interface CommandClientMessage<T extends string> extends BaseMessage {
  // 添加新命令到条件类型
  payload: T extends 'new_command' ? NewCommandPayload : 
           // ... 其他命令
           never;
}

// 添加到联合类型
export type ClientMessage =
  | ChatClientMessage
  | CommandClientMessage<'new_command'>
  | // ... 其他命令
```

#### 3. 实现前端处理
```typescript
// src/stores/chat.ts
const sendNewCommand = (param1: string, param2?: number) => {
  chatStore.sendCommand('new_command', { param1, param2 }, '[执行新命令]');
};
```

#### 4. 实现后端处理
```typescript
// 后端命令处理器
case 'new_command':
  return await executeNewCommand(payload);
```

### 调试技巧

#### 1. 开发环境日志
```typescript
if (import.meta.env.DEV) {
  console.log('Sending message:', message);
  console.log('SSE Event:', event);
}
```

#### 2. 网络监控
- 使用浏览器开发者工具的Network标签
- 查看SSE连接状态和事件流

#### 3. 错误追踪
- 使用requestId进行端到端追踪
- 检查timestamp确保事件顺序

### 最佳实践

### 通用原则
1. **消息ID生成**: 使用UUID或时间戳确保唯一性
2. **错误处理**: 总是提供有意义的错误消息和重试建议
3. **超时处理**: 设置合理的超时时间避免连接悬挂
4. **资源清理**: 及时清理SSE连接和AbortController
5. **类型安全**: 充分利用TypeScript的类型检查
6. **统一请求工具**: 生产环境使用 `sseRequest` 而非直接的 `fetch`，确保认证和错误处理的一致性

### 流式命令最佳实践

#### 何时使用流式输出
- **长时间执行的操作**：设备查询、系统诊断、数据分析等
- **需要进度反馈的任务**：文件处理、批量操作等
- **用户需要了解执行过程的命令**：调试、故障排查等

#### 何时使用非流式输出
- **快速执行的操作**：简单的状态切换、配置更新等
- **结果明确的命令**：重启、删除、创建等
- **不需要过程反馈的任务**：简单查询、验证操作等

#### 流式命令设计原则
1. **有意义的进度信息**：每个步骤都应该提供有价值的反馈
2. **合理的时间间隔**：避免过于频繁或过于稀少的更新
3. **清晰的状态指示**：使用图标、颜色等视觉元素区分不同状态
4. **优雅的错误处理**：流式过程中的错误应该清晰地显示在日志中
5. **可取消性**：长时间运行的流式命令应该支持用户取消

#### 前端实现要点
```typescript
// 配置流式命令列表
const streamingCommands = ['query_device', 'system_diagnosis', 'batch_process'];

// 为流式命令提供丰富的视觉反馈
const getCommandIcon = (commandType: string) => {
  const icons = {
    'query_device': DesktopOutline,
    'vm_reset': ServerOutline,
    'ticket_preassign': DocumentTextOutline
  };
  return icons[commandType] || DocumentTextOutline;
};
```

#### 后端实现要点
```typescript
// 流式命令应该提供结构化的日志输出
const sendProgressLog = (step: string, details?: any) => {
  sseStream.sendEvent('token_chunk', {
    content: `${step}${details ? `\n${JSON.stringify(details, null, 2)}` : ''}\n`
  }, requestId);
};

// 合理控制输出频率
await delay(200); // 避免过于频繁的更新
```


## 命令流式输出总结

| 特性 | 流式命令 | 非流式命令 |
|------|----------|------------|
| **消息类型** | `assistant` | `system` |
| **事件序列** | `token_chunk` → `command_receipt` → `stream_end` | `command_receipt` → `stream_end` |
| **用户体验** | 实时进度反馈 | 简洁结果显示 |
| **适用场景** | 长时间执行、需要过程反馈 | 快速执行、结果明确 |
| **视觉标识** | 蓝色左边框、命令图标 | 黄色背景、系统样式 |
| **内容更新** | 累积式追加 | 一次性替换 |

### 扩展新命令

1. **添加到流式命令列表**（如需要）：
   ```typescript
   const streamingCommands = ['query_device', 'new_streaming_command'];
   ```

2. **定义命令载荷类型**：
   ```typescript
   interface NewCommandPayload { /* ... */ }
   ```

3. **更新命令联合类型**：
   ```typescript
   export type ClientMessage = /* ... */ | CommandClientMessage<'new_command'>;
   ```

4. **实现后端处理逻辑**：
   - 流式命令：发送多个 `token_chunk`
   - 非流式命令：直接发送 `command_receipt`

---

*本文档版本: 1.1*
*最后更新: 2024-07-14*
*新增: 命令流式输出配置和实现指南*
