
# 智能工单分配系统设计文档

## 1. 背景与目标

当前的工单分配系统采用基于简单规则的“传统分配方式”，其灵活性和智能化程度有限。为提升分配效率与准确性，我们设计并实现一个**基于多智能体（Multi-Agent）的智能工单分配系统**。

该系统旨在通过模拟不同角色的智能体协同工作，实现更精细、动态和智能的工单分配。同时，系统将兼容现有的传统分配方式，作为可靠的降级方案（Fallback），确保在任何情况下系统的稳定运行。

## 2. 系统架构

系统的核心是多个独立但协同工作的智能体，它们共享一个统一的知识库，并利用外部工具与系统交互。

```mermaid
graph LR
    Start[新工单来临] --> Coordinator[协调者]

    Coordinator --> IntelligentAssigner[智能分配者]
    Coordinator --> LegacyAssigner[传统分配者]
    Coordinator --> Evaluator[分配评估者]
    
    Evaluator --> Coordinator
    IntelligentAssigner --> Coordinator
    LegacyAssigner --> Coordinator

    Coordinator --> End[End]
```

### 2.1 智能体 (Agents)

**协调者 (Coordinator)**
作为工作流的指挥中心，负责整个分配过程的调度与控制。
  - 接收新工单，作为分配流程的入口。
  - 解析用户输入和系统宏观策略，决策采用何种分配模式。
  - 在不同智能体之间委派任务、传递信息和反馈。
  - 监控并处理其他智能体的执行状态（成功、失败、异常）。
  - 记录最终分配结果，并同步至相关系统。

**智能工单分配者 (Intelligent Assigner)**
利用AI/LLM算法执行核心的智能分配任务。
  - 基于对工单内容、历史工单、员工画像和系统状态的深度理解，计算出最优的工单承接人。
  - 生成分配决策的理由，增强结果的可解释性。
  - 将分配结果或执行中的异常（如LLM API超时）返回给**协调者**。

**传统工单分配者 (Legacy Assigner)**
封装了原有的基于规则的分配算法。
  - 作为**智能工单分配者**的降级方案。
  - 在**协调者**的指令下（例如，AI服务不可用或性能低下时）执行分配。

**工单分配评估者 (Assignment Evaluator)**
对分配结果进行复盘和校验，提供反馈以启动修正流程。
  - 在分配完成后，根据指令对分配结果进行合理性评估。
  - 将评估发现的问题（如分配给已离职员工）报告给**协调者**。
  - 评估结果可用于触发一次**二次分配**。

### 2.2 共享知识库 (Knowledge Base)

知识库为所有智能体提供决策所需的宏观信息，并作为持久化层，存储关键数据以供未来分析与优化。

- **工单知识库 (`tickets`)**
  - **内容**：存储历史和进行中的工单信息，是智能分配的基础。
  - **字段示例**：
    - **ID**: 工单唯一标识
    - **服务类型 (Service Package)**: 工单的分类（如：维修、安装、巡检）
    - **处理人 (Assignee)**: 该工单的负责人
    - **创建/更新/完成日期**: 工单的时间戳信息
    - **状态 (Status)**: 工单当前状态（待分配、进行中、已完成、已关闭）
    - **描述 (Description)**: 工单内容的详细文本描述，包含操作地点、涉及设备、操作内容等
    - **...**: 系统需要的其他来自JIRA系统的字段
    *为了区分，下面所有的字段都是工单系统的内部字段(以internal_开头)*
    - **分类 (catalog)**: 内部分类, 比如来自不同系统的标识(预留，还没有使用)
    - **描述 (filtered_desc)**: 预处理后的描述
    - **描述特征向量**: 预处理后描述字段进行向量化表示，用于高效计算相似度
    - **智能分配结果与理由**: 记录AI分配决策及依据
    - **人工反馈与标注**: 用于模型评估与优化

- **员工画像库 (`profiles`)**
  - **内容**：维护所有可能承接工单的员工的动态信息。
  - **字段示例**：
    - **姓名/ID**: 员工唯一标识
    - **能力标签 (Skill Tags)**: 描述员工专业技能、擅长领域的标签
    - **最近动态 (Recent Activity)**: 员工近期完成工单的总结，反映其近期工作经验
    - **任务队列长度**: 实时工作负载
    - **个人意愿**: 如希望处理特定领域的任务
    - **状态**:  员工当前状态，如在职、休假、离职等 （统管理员分配）
    - **分配的地点列表 (components)**:   工作地点列表 （统管理员分配）
    - **分配的内容列表 (servicepages)**: 服务的service packages的类型 （系统管理员分配）

- **系统状态库 (`system_status`)**
  - **内容**：记录影响分配策略的外部系统状态。
  - **字段示例**：LLM API的可用性、平均延迟、Token消耗成本等。

### 2.3 外部工具 (Tools)

智能体通过封装好的工具与外部系统交互。

- **Jira 工具**: 提供创建、更新和查询 Jira 工单的接口。
- **知识库召回工具**: 提供对上述共享知识库（`tickets`, `profiles`, `system_status`）的高效查询与检索接口。

## 3. 核心工作流

1.  **启动**: 新工单送达，**协调者**作为工作流入口被激活。

2.  **宏观策略路由**: **协调者**首先进入“宏观策略”节点，根据预设策略进行初步判断和路由。
    - **策略来源**: 根据工单本身的信息， 以及系统级配置，而不需要依赖知识库和外部工具来做决策。如强制传统模式、或基于用户配置的宏观规则动态决策。

    - **实现方式**:
        - **基于规则**: 简单、稳定。
        - **基于LLM**: 灵活，能处理更复杂的场景，但需考虑其可用性。
        > *示例规则 1: “所有‘焊接’类任务，直接分配给工程师A。”*
        > *示例规则 2: “若工单的‘组件’与‘目标位置’不匹配，则标记为无效并中止分配。”*
    - **决策输出**: 委派给特定智能体，或直接分配转入记录。

3.  **智能分配与降级**:
    - **协调者**默认调用**智能工单分配者**。
    - 若**智能工单分配者**返回失败信号（如LLM服务异常），**协调者**将立即调用**传统工单分配者**作为兜底方案，确保业务连续性。

4.  **评估与反思 (Optional)**:
    - 根据配置，分配完成后，**协调者**可要求**分配评估者**进行一次“反思”（Reflection）。
    - **分配评估者**对结果进行校验。
      > *场景示例：**智能分配者**将工单分给了一位刚离职的员工。**评估者**通过查询员工画像库发现了该问题，并向**协调者**报告。*
    - **协调者**接收到评估反馈后，可将修正信息（如“员工A已离职，请重新选择”）传递给**智能分配者**，启动一次**二次分配**。
    - *（此环节的评估策略与数据源需进一步详细设计）*

5.  **记录与终结**:
    - 一旦分配完成（成功、失败或跳过），**协调者**在退出流程前，负责将最终结果、决策理由和过程日志更新到 **Jira** 和**共享知识库**中。

## 4. 可靠性设计

为确保系统的稳定、可预测和可维护，我们遵循以下设计原则：

- **优先使用确定性逻辑**: 凡是可以通过明确规则或逻辑判断处理的环节，应避免使用LLM，以保证结果的稳定性和可预测性。

- **健壮的异常处理**: 需为所有外部调用（如LLM、Jira API）配置合理的超时时间和重试机制，并定义清晰的异常处理流程。

- **避免无限循环**: **协调者**的逻辑至关重要。必须设计机制来防止因智能体异常或LLM幻觉导致的无限循环，例如通过限制单一工单的分配重试次数或调用深度。

- **详尽的数据记录**: 完整记录每次分配的输入、中间过程、决策理由和最终结果。这些数据不仅用于问题排查，更是未来进行策略优化和模型微调（Fine-tuning）。


## 5. 详细设计

### 5.1 知识库构建

```mermaid
flowchart TD
    subgraph 知识库构建与更新
        F[Jira系统数据源] --> G[清洗、向量化及其他预处理]
        G --> H[工单知识库]
        H --> I[员工历史工单数据]
        I --> J[LLM生成能力标签]
        J --> K[人工审核标注]
        K --> L[员工知识库]
        I --> M[LLM总结最近动态]
        M --> L
    end

    %% 统一样式定义
    classDef main fill:#f0f8ff,stroke:#4682b4,stroke-width:2px
    classDef process fill:#e6e6fa,stroke:#9370db
    classDef db fill:#e0ffff,stroke:#20b2aa
    classDef decision fill:#ffebcd,stroke:#ffa500

    %% 应用样式
    class A,E,F main
    class B,B1,B2,B3,B4,B5,C,C1,C2,D,D1,D2,D3,G,J,K,M process
    class H,I,L db
```

#### 工单知识库(tickets)抽取与处理

1.  **数据源**: 公司内部工单系统（如Jira）。
2.  **抽取方式**:
    *   **定时增量抓取**: 例如每小时或每日数次。
    *   **实时同步机制 (可选)**: 考虑使用Webhook等机制，在工单数据更新时实时同步，以保证数据一致性。
3.  **数据清洗**: 清理“描述”字段中的无关信息、格式化文本，提取核心信息（如地点、设备、操作）。
4.  **向量化**: 调用向量模型（Embedding Model）将清洗后的“描述”字段转换为特征向量，并存储在向量数据库。
5.  **持续更新**: 后台任务持续进行，确保知识库信息的时效性。


#### 员工画像库(profiles)构建

1.  **能力标签**:
    *   **LLM生成**: 基于员工历史处理工单的描述，利用LLM初步生成能力标签。
    *   **人工审核与标注**: 对LLM生成的标签进行人工审核、修正和补充。
2.  **最近动态**:
    *   **LLM总结**: 定期（如每日）提取员工最近一个月已完成工单的描述，使用LLM进行总结，形成结构化或半结构化的动态信息, 表示该员工最近在哪些位置，设备做过哪些事情。
    *   **直接拼接**: 或直接将相关工单的关键描述字段进行拼接。
3.  **工作负载**: 通过数据库函数实时计算员工当前“进行中”的工单数量。
4.  **员工状态**: 手工标注?（如在职、休假、离职等）。
5.  **状态**:  员工当前状态，如在职、休假、离职等 （统管理员分配, 需要UI以及和已有数据融合）
6.  **分配的地点列表 (components)**:   工作地点列表 （统管理员分配, 需要UI以及和已有数据融合）
7.  **分配的内容列表 (servicepages)**: 服务的service packages的类型 （系统管理员分配, 需要UI以及和已有数据融合）
3.  **信息来源**: 主要来源于“工单知识库”中的工单数据进行的二次计算。

### 5.2 智能分配者流程


```mermaid
flowchart TD
    A[协调者派发工单] --> B[信息预处理]
    B --> C[历史工单筛选]
    B --> D[员工筛选]
    C & D --> E[LLM决策]
    E --> F[协调者]

    subgraph B[信息预处理]
        direction TB
        B1[提取类型] --> B2[清洗描述] --> B3[向量化描述]
    end

    subgraph C[历史工单筛选]
        direction TB
        C1[筛选类型、时间范围、状态] --> C2[匹配相似度] --> C3[选取topN历史记录]
    end

    subgraph D[员工筛选]
        direction TB
        D1[工作状态，负载、servicepages, components 筛选] --> D2[获取能力标签和最近动态]
    end

    subgraph E[LLM决策]
        direction TB
        E1[整合信息
        新工单详情
        相似历史工单
        候选员工信息] --> E2[LLM评估
        能力匹配
        经验相关性
        工作负载] --> E3[输出推荐人选及理由]
    end

    style A fill:#FFD700,stroke:#333
    style B fill:#A6E3E9,stroke:#333
    style C fill:#C9CBFF,stroke:#333
    style D fill:#FFB6C1,stroke:#333
    style E fill:#98FB98,stroke:#333
    style F fill:#FFA07A,stroke:#333
```

当一个新工单进入系统时：

1.  **历史工单匹配**: 为了寻找在最近一段时间，在相同的地方相同的设备的类似任务
    *   提取新工单“描述”字段, 清洗并其向量化。
    *   在“工单知识库”中，筛选出与新工单“服务类型”且<span style="color:red">component</span>相同，且状态为“已完成”和“进行中”（根据策略可选）的最近（如过去一个月）的工单。
    *   对筛选出的工单，通过其“描述特征向量”与新工单的“描述特征向量”进行相似度匹配（如余弦相似度）。
    *   选取匹配度最高的N条历史工单（例如15条）作为参考。

2.  **候选员工筛选**: 寻找哪些员工适合做这个任务
    *   从“员工画像” 知识库中查找工作量合适（如少于7个）的员工，并且能接受任务的员工（没有离职请假）。
    *   <span style="color:red">员工的其他匹配 (比如工作的楼层component， 领域service package) 规则，这些好像在传统的分配者中必须满足的规则。</span>
    *   从“员工画像库”中获取这些符合条件员工的“能力标签”， "个人意愿"， “最近动态”信息。

3.  **LLM决策与推荐**:
    *   **信息整合**: 将以下信息结构化后提交给LLM：
        *   新工单的详细信息（服务类型、描述原文、操作内容、目标位置、涉及设备等关键信息）。
        *   步骤1中匹配到的Top N条相似历史工单信息。
        *   步骤2中筛选出的候选员工列表，及其对应的能力标签、个人意愿、最近动态和当前工作负载。
    *   **LLM推理**: 指示LLM基于以下维度进行综合评估和推理：
        *   **能力匹配**: 员工的“能力标签”与新工单所需技能的吻合度。
        *   **经验相关性**: 员工“最近动态”中体现的与新工单在工作内容、地点、设备上的相关性。
        *   **工作负载**: 确保员工有足够的精力处理新工单。
    *   **输出**: LLM输出最合适的员工人选，并提供详细的推荐理由，说明其在能力、历史经验、工作负载等方面的优势。

