# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Development
- `npm run dev` - Start development server on port 5173
- `npm run build` - Build for production (includes TypeScript compilation)
- `npm run preview` - Preview production build locally

### Docker Commands
- `./scripts/dev.sh` - Start development environment with Docker Compose
- `./scripts/build.sh` - Build and deploy production environment with Docker
- `docker compose up -d` - Start production services
- `docker compose down` - Stop all services

### Code Quality
The project uses TypeScript with strict checking. Run `vue-tsc -b` to check types before building.

## Architecture Overview

### Frontend Stack
- **Vue 3** with Composition API and `<script setup>` syntax
- **TypeScript** for type safety
- **Vite** as build tool and dev server
- **Naive UI** component library with custom theming
- **Pinia** for state management with persistence
- **Vue Router** for navigation
- **Tailwind CSS** for styling with custom dark theme

### Key Architectural Patterns

#### State Management (Pinia Stores)
- `stores/ticket.ts` - Central ticket data management with unified storage approach
- `stores/ui.ts` - UI settings, theme, and configuration
- `stores/user.ts` - User session and preferences
- `stores/chat.ts` - Chat/messaging state

#### API Layer
- `api/ticket.ts` - Ticket CRUD operations with mock data fallback
- `api/chatSSE.ts` - Server-Sent Events for real-time chat communication
- `utils/request.ts` - Centralized HTTP client with proxy configuration

#### Component Structure
- `layouts/MainLayout.vue` - Main application shell
- `views/` - Page-level components (Dashboard, TicketView, etc.)
- `components/` - Reusable UI components (TicketList, ChatbotPanel, etc.)

### Data Flow Architecture

#### Ticket Management
The application uses a unified ticket storage approach where all tickets (created and assigned) are stored in a single `allTickets` array in the ticket store. Computed properties filter this data based on user email to show relevant tickets.

#### Real-time Communication
Uses SSE (Server-Sent Events) for chat functionality with a structured protocol defined in `types/protocol.ts`. Supports both chat messages and command execution with streaming responses.

## Project Structure

### Source Organization
```
src/
├── api/          # API layer and external service integration
├── components/   # Reusable Vue components
├── composables/  # Vue composition functions
├── layouts/      # Layout components
├── router/       # Vue Router configuration
├── stores/       # Pinia state management
├── types/        # TypeScript type definitions
├── utils/        # Shared utilities and helpers
└── views/        # Page-level components
```

### Configuration Files
- `vite.config.ts` - Build configuration with proxy setup for development
- `tailwind.config.js` - Styling configuration with custom dark theme
- `tsconfig.*.json` - TypeScript configuration for app and build tools
- `docker-compose.yml` - Production deployment configuration

## Development Environment

### Proxy Configuration
Development server proxies `/api` requests to `https://ci.labtools.china.nsn-net.net` with custom CORS handling and SSL configuration.

### Theme System
Implements a comprehensive dark/light theme system using:
- Tailwind CSS custom color palette
- Naive UI theme overrides
- CSS custom properties for smooth transitions

### Mock Data
Development environment includes comprehensive mock data for tickets and SSE responses. Enable with `VITE_USE_MOCK_DATA=true`.

## Key Implementation Details

### Ticket Store Logic
- Unified storage approach: all tickets in single array
- Filtered computed properties for created/assigned views
- Automatic cache management with configurable refresh intervals
- Persistent storage with localStorage

### SSE Communication
- Protocol-based message structure with type safety
- Support for streaming responses (chat and command execution)
- Automatic reconnection and error handling
- Mock implementation for development

### UI/UX Features
- Responsive design with mobile support
- Smooth theme transitions
- Real-time status updates
- Advanced markdown rendering for chat
- Configurable refresh intervals and data retention

## Base URL Configuration
Application is configured to run under `/iticket/` base path for deployment behind reverse proxies.