/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  darkMode: 'class', // 启用类模式的深色主题
  theme: {
    extend: {
      colors: {
        // 主色调 - 使用蓝色系作为主色调
        primary: {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
        },
        // 统一的深色主题颜色系统
        dark: {
          // 背景色系统
          bg: '#1f2937',           // 主背景
          'bg-secondary': '#111827', // 次要背景
          surface: '#374151',       // 卡片/面板背景
          'surface-hover': '#4b5563', // 悬停状态

          // 边框色系统
          border: '#4b5563',        // 主边框
          'border-light': '#6b7280', // 浅边框

          // 文字色系统
          text: '#f9fafb',          // 主文字
          'text-secondary': '#e5e7eb', // 次要文字
          'text-muted': '#d1d5db',   // 弱化文字
          'text-disabled': '#9ca3af', // 禁用文字

          // 状态色
          accent: '#3b82f6',        // 强调色
          success: '#10b981',       // 成功色
          warning: '#f59e0b',       // 警告色
          error: '#ef4444',         // 错误色
        }
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      },
      fontFamily: {
        'sans': ['Segoe UI', 'Tahoma', 'Geneva', 'Verdana', 'sans-serif'],
      }
    },
  },
  plugins: [],
  corePlugins: {
    preflight: true, // 启用样式重置以获得更好的基础样式
  }
}