# 多阶段构建 - 构建阶段
FROM node:20-alpine AS builder

#设置环境变量
ARG HTTP_PROXY=http://10.144.1.10:8080
ARG HTTPS_PROXY=http://10.144.1.10:8080
ENV HTTP_PROXY=http://10.144.1.10:8080
ENV HTTPS_PROXY=http://10.144.1.10:8080


# 设置工作目录
WORKDIR /app

# 复制package文件并安装依赖（利用Docker缓存层）
COPY package*.json ./
RUN npm ci --silent

# 复制源代码
COPY . .

# 设置构建时环境变量以使用 mock 数据
ARG VITE_USE_MOCK_DATA=true
ENV VITE_USE_MOCK_DATA=$VITE_USE_MOCK_DATA

# 构建应用
RUN npm run build

# 生产阶段 - 使用最新的nginx
FROM nginx:alpine

# nginx:alpine镜像已经包含nginx用户，无需重新创建

# 复制构建产物到nginx目录的iticket子目录
COPY --from=builder /app/dist /usr/share/nginx/html/iticket

# 复制nginx配置文件
COPY nginx.conf /etc/nginx/nginx.conf

# 设置正确的权限
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    chown -R nginx:nginx /var/cache/nginx && \
    chown -R nginx:nginx /var/log/nginx && \
    chown -R nginx:nginx /etc/nginx/conf.d

# 创建nginx运行时需要的目录
RUN touch /var/run/nginx.pid && \
    chown -R nginx:nginx /var/run/nginx.pid

# 暴露端口
EXPOSE 80

# 使用非root用户运行
USER nginx

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]
