
services:
  # 前端应用
  frontend:
    # build:
    #  context: .
    #  dockerfile: Dockerfile
      # 使用BuildKit加速构建
      #  cache_from:
      #  - node:20-alpine
      #  - nginx:alpine
    image: ${REGISTRY}/vue-ticket-frontend:${TAG}
          #image: vue-ticket-frontend:eng
    container_name: vue-ticket-frontend
    ports:
      - "8110:80"
    environment:
      - NODE_ENV=production
      - VITE_USE_MOCK_DATA=true
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://127.0.0.1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - ticket-network
    # 资源限制
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.1'
          memory: 128M

  # 后端服务（示例配置，根据实际后端调整）
  # backend:
  #   image: your-backend-image:latest
  #   container_name: vue-ticket-backend
  #   ports:
  #     - "8080:8080"
  #   environment:
  #     - NODE_ENV=production
  #     - DATABASE_URL=**********************************/ticket_db
  #   restart: unless-stopped
  #   depends_on:
  #     - db
  #   networks:
  #     - ticket-network

  # 数据库服务（示例配置）
  # db:
  #   image: postgres:15-alpine
  #   container_name: vue-ticket-db
  #   environment:
  #     - POSTGRES_DB=ticket_db
  #     - POSTGRES_USER=user
  #     - POSTGRES_PASSWORD=password
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #   restart: unless-stopped
  #   networks:
  #     - ticket-network

  # Redis缓存（示例配置）
  # redis:
  #   image: redis:7-alpine
  #   container_name: vue-ticket-redis
  #   restart: unless-stopped
  #   networks:
  #     - ticket-network

networks:
  ticket-network:
    driver: bridge

# volumes:
#   postgres_data:
