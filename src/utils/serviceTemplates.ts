import type { ServiceItem } from '@/types/ticket';
import { generateServiceItemId } from './servicePackages';

// Service template definition
export interface ServiceTemplate {
  id: string;
  name: string;
  description: string;
  servicePackageId: string;
  items: Omit<ServiceItem, 'id'>[];
}

// Software installation templates
export const softwareTemplates: ServiceTemplate[] = [
  {
    id: 'dev-environment',
    name: 'Development Environment Suite',
    description: 'Common software packages for frontend development',
    servicePackageId: 'software-installation',
    items: [
      {
        data: {
          software_name: 'Visual Studio Code',
          version: '1.85.0',
          installation_reason: 'Code editor for frontend development'
        }
      },
      {
        data: {
          software_name: 'Node.js',
          version: '20.10.0',
          installation_reason: 'JavaScript runtime environment, essential for frontend development'
        }
      },
      {
        data: {
          software_name: 'Git',
          version: '2.43.0',
          installation_reason: 'Version control tool'
        }
      },
      {
        data: {
          software_name: 'Chrome',
          version: 'Latest',
          installation_reason: 'Browser for testing and debugging'
        }
      }
    ]
  },
  {
    id: 'design-tools',
    name: 'Design Tools Suite',
    description: 'Common software for UI/UX design',
    servicePackageId: 'software-installation',
    items: [
      {
        data: {
          software_name: 'Figma Desktop',
          version: 'Latest',
          installation_reason: 'UI design and prototyping'
        }
      },
      {
        data: {
          software_name: 'Adobe Photoshop',
          version: '2024',
          installation_reason: 'Image processing and design'
        }
      },
      {
        data: {
          software_name: 'Adobe Illustrator',
          version: '2024',
          installation_reason: 'Vector graphics design'
        }
      }
    ]
  },
  {
    id: 'office-suite',
    name: 'Office Software Suite',
    description: 'Essential software for daily office work',
    servicePackageId: 'software-installation',
    items: [
      {
        data: {
          software_name: 'Microsoft Office 365',
          version: 'Latest',
          installation_reason: 'Document processing, spreadsheet creation, presentations'
        }
      },
      {
        data: {
          software_name: 'WPS Office',
          version: 'Latest',
          installation_reason: 'Alternative office software'
        }
      },
      {
        data: {
          software_name: 'Zoom',
          version: 'Latest',
          installation_reason: 'Video conferencing software'
        }
      },
      {
        data: {
          software_name: 'WeChat Work',
          version: 'Latest',
          installation_reason: 'Enterprise communication tool'
        }
      }
    ]
  }
];

// Asset transfer templates
export const assetTemplates: ServiceTemplate[] = [
  {
    id: 'office-relocation',
    name: 'Office Relocation',
    description: 'Asset transfer for complete office relocation',
    servicePackageId: 'asset-transfer',
    items: [
      {
        data: {
          asset_number: 'IT-DESK-001',
          serial_number: 'DESK001',
          sending_site: 'Beijing Office - 3F Development Dept',
          receiving_site: 'Shanghai Office - 2F Sales Dept',
          description: 'Office desk and supporting facilities'
        }
      },
      {
        data: {
          asset_number: 'IT-CHAIR-001',
          serial_number: 'CHAIR001',
          sending_site: 'Beijing Office - 3F Development Dept',
          receiving_site: 'Shanghai Office - 2F Sales Dept',
          description: 'Office chair'
        }
      }
    ]
  }
];

// Get templates for specified service package
export function getTemplatesForServicePackage(servicePackageId: string): ServiceTemplate[] {
  switch (servicePackageId) {
    case 'software-installation':
      return softwareTemplates;
    case 'asset-transfer':
      return assetTemplates;
    default:
      return [];
  }
}

// Apply template to service items
export function applyTemplate(template: ServiceTemplate): ServiceItem[] {
  return template.items.map(item => ({
    id: generateServiceItemId(),
    data: { ...item.data }
  }));
}

// Create custom template
export function createCustomTemplate(
  name: string,
  description: string,
  servicePackageId: string,
  items: ServiceItem[]
): ServiceTemplate {
  return {
    id: `custom-${Date.now()}`,
    name,
    description,
    servicePackageId,
    items: items.map(item => ({ data: { ...item.data } }))
  };
}
