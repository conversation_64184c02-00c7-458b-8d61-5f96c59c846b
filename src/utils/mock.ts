import { OPTIONS_KEYS } from "@/types/form";

export const priorityOptions = [
  { label: "Minor", value: "Minor" },
  { label: "Low", value: "Low" },
  { label: "Medium", value: "Medium" },
  { label: "High", value: "High" },
  { label: "Critical", value: "Critical" },
];

export const componentsOptions = [
  {
    label: "Beijing Office",
    value: "Beijing Office",
    children: [
      { label: "1F Reception", value: "Beijing Office - 1F Reception" },
      { label: "2F Finance Dept", value: "Beijing Office - 2F Finance Dept" },
      {
        label: "3F Development Dept",
        value: "Beijing Office - 3F Development Dept",
      },
      { label: "4F Design Dept", value: "Beijing Office - 4F Design Dept" },
      {
        label: "5F Conference Room",
        value: "Beijing Office - 5F Conference Room",
      },
    ],
  },
  {
    label: "Shanghai Office",
    value: "Shanghai Office",
    children: [
      { label: "1F Lobby", value: "Shanghai Office - 1F Lobby" },
      { label: "2F Sales Dept", value: "Shanghai Office - 2F Sales Dept" },
      {
        label: "3F Marketing Dept",
        value: "Shanghai Office - 3F Marketing Dept",
      },
    ],
  },
  {
    label: "Shenzhen Office",
    value: "Shenzhen Office",
    children: [
      {
        label: "1F Reception Area",
        value: "Shenzhen Office - 1F Reception Area",
      },
      {
        label: "2F Technical Dept",
        value: "Shenzhen Office - 2F Technical Dept",
      },
    ],
  },
  {
    label: "Data Center",
    value: "Data Center",
    children: [
      {
        label: "Zone A Server Room",
        value: "Data Center - Zone A Server Room",
      },
      {
        label: "Zone B Server Room",
        value: "Data Center - Zone B Server Room",
      },
      { label: "Zone C Storage", value: "Data Center - Zone C Storage" },
    ],
  },
  {
    label: "Remote Work",
    value: "Remote Work",
    children: [{ label: "Home", value: "Remote Work - Home" }],
  },
  {
    label: "Customer Site",
    value: "Customer Site",
    children: [
      {
        label: "Temporary Deployment",
        value: "Customer Site - Temporary Deployment",
      },
    ],
  },
];

export const servicePackageOptions = [
  {
    label: "HW and Lab Infra Service",
    value: "hw",
    fields: [],
    children: [
      {
        label: "HW (un)installaticon/local relocation",
        value: "hw-1",
        fields: [
          {
            modelKey: "hw_name",
            modelValue: "",
            type: "input",
            label: "HW Name",
            placeholder: "please enter hw name",
            required: true,
            trigger: "blur",
            message: "Please enter a value",
            props: {}
          },
          {
            modelKey: "serial_number",
            modelValue: "",
            type: "input",
            label: "Serial number",
            placeholder: "please enter serial number",
            required: true,
            trigger: "blur",
            message: "Please enter a value",
            props: {}
          },
          {
            modelKey: "installation_location",
            modelValue: "",
            type: "input",
            label: "Installation Location",
            placeholder: "please enter installation Location",
            required: true,
            trigger: "blur",
            message: "Please enter a value",
            props: {}
          }
        ],
      }
    ],
  },
];



export const FORM_LIST = [
  { 
    modelKey: 'summary', 
    modelValue: '', 
    modelType: 'string', 
    type: 'input', 
    label: 'Summary', 
    placeholder: 'please enter a value', 
    required: true,
    message: 'please enter a value'
  },
  { 
    modelKey: 'priority', 
    modelValue: '', 
    modelType: 'string', 
    type: 'select', 
    label: 'Priority', 
    placeholder: 'please enter a value', 
    optionKey: OPTIONS_KEYS['PRIORITY'] 
  },
  { 
    modelKey: 'components', 
    modelValue: '',
    modelType: 'string', 
    type: 'select', 
    label: 'Components', 
    placeholder: 'please enter a value', 
    optionKey: OPTIONS_KEYS['COMPONENTS'] 
  },
  { 
    modelKey: 'service_package', 
    modelValue: '', 
    modelType: 'string', 
    type: 'cascader', 
    label: 'Service Packages', 
    placeholder: 'please enter a value', 
    optionKey: OPTIONS_KEYS['SERVICE_PACKAGE'],
    props: {
      'on-update:value': () => {
        console.log('??xxxzz')
      }
    }
  },
]

