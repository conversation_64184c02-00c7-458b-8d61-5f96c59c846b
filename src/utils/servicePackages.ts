import type { ServicePackage, ServiceItem } from '@/types/ticket';

export const servicePackages: ServicePackage[] = [
  {
    id: 'network-troubleshooting',
    name: 'Network Troubleshooting',
    allowMultiple: false,
    fields: [
      {
        name: 'issue_description',
        label: 'Issue Description',
        type: 'textarea',
        required: true,
        width: 'large',
        placeholder: 'Please describe the specific symptoms of the network issue in detail'
      }
    ]
  },
  {
    id: 'asset-transfer',
    name: 'Asset Transfer',
    allowMultiple: true,
    maxItems: 10,
    itemName: 'Asset',
    fields: [
      {
        name: 'asset_number',
        label: 'Asset Number',
        type: 'input',
        required: true,
        width: 'medium',
        placeholder: 'e.g.: IT-2024-001'
      },
      {
        name: 'serial_number',
        label: 'Serial Number',
        type: 'input',
        required: true,
        width: 'medium',
        placeholder: 'Device serial number'
      },
      {
        name: 'sending_site',
        label: 'Sending Site',
        type: 'select',
        options: [
          'Beijing Office - 1F Reception',
          'Beijing Office - 2F Finance Dept',
          'Beijing Office - 3F Development Dept',
          'Beijing Office - 4F Design Dept',
          'Beijing Office - 5F Conference Room',
          'Shanghai Office - 1F Lobby',
          'Shanghai Office - 2F Sales Dept',
          'Shanghai Office - 3F Marketing Dept',
          'Shenzhen Office - 1F Reception Area',
          'Shenzhen Office - 2F Technical Dept'
        ],
        required: true,
        width: 'medium',
        placeholder: 'Sending location'
      },
      {
        name: 'receiving_site',
        label: 'Receiving Site',
        type: 'select',
        options: [
          'Beijing Office - 1F Reception',
          'Beijing Office - 2F Finance Dept',
          'Beijing Office - 3F Development Dept',
          'Beijing Office - 4F Design Dept',
          'Beijing Office - 5F Conference Room',
          'Shanghai Office - 1F Lobby',
          'Shanghai Office - 2F Sales Dept',
          'Shanghai Office - 3F Marketing Dept',
          'Shenzhen Office - 1F Reception Area',
          'Shenzhen Office - 2F Technical Dept'
        ],
        required: true,
        width: 'medium',
        placeholder: 'Receiving location'
      },
      {
        name: 'description',
        label: 'Description',
        type: 'textarea',
        required: true,
        width: 'large',
        placeholder: 'Asset description and transfer reason'
      }
    ]
  },
  {
    id: 'software-installation',
    name: 'Software Installation',
    allowMultiple: true,
    maxItems: 20,
    itemName: 'Software',
    fields: [
      {
        name: 'software_name',
        label: 'Software Name',
        type: 'select',
        required: true,
        options: [
          'Visual Studio Code',
          'Node.js',
          'Git',
          'Chrome',
          'Figma Desktop',
          'Adobe Photoshop',
          'Adobe Illustrator',
          'Microsoft Office 365',
          'WPS Office',
          'Zoom',
          'WeChat Work'
        ],
        width: 'medium',
        placeholder: 'e.g.: Visual Studio Code'
      },
      {
        name: 'version',
        label: 'Version',
        type: 'input',
        required: false,
        width: 'small',
        placeholder: 'e.g.: 1.85.0'
      },
      {
        name: 'installation_reason',
        label: 'Installation Reason',
        type: 'textarea',
        required: true,
        width: 'large',
        placeholder: 'Installation reason and usage description'
      }
    ]
  }
];

// Serialize form data - supports single and multiple items
export function serializeFormData(servicePackageId: string, formData: Record<string, any> | ServiceItem[]): string {
  const servicePackage = servicePackages.find(sp => sp.id === servicePackageId);
  if (!servicePackage) return '';

  const headers = servicePackage.fields.map(field => field.label);
  const headerRow = `||NO.||${headers.join('||')}||%0A`;

  // Handle multiple items data
  if (servicePackage.allowMultiple && Array.isArray(formData)) {
    const valueRows = formData.map((item, index) => {
      const values = servicePackage.fields.map(field => item.data[field.name] || '');
      return `|${index + 1}|${values.join('|')}|`;
    }).join('%0A');

    return headerRow + valueRows;
  }

  // Handle single item data (backward compatibility)
  const values = servicePackage.fields.map(field => (formData as Record<string, any>)[field.name] || '');
  const valueRow = `|1|${values.join('|')}|`;

  return headerRow + valueRow;
}

// Parse description data - supports single and multiple items
export function parseDescription(description: string, servicePackageId: string): Record<string, any> | ServiceItem[] | { rawContent: string } {
  const servicePackage = servicePackages.find(sp => sp.id === servicePackageId);

  // If no service package found or no description, return raw content
  if (!servicePackage) {
    return description ? { rawContent: description } : { rawContent: '' };
  }

  if (!description) return servicePackage.allowMultiple ? [] : {};

  try {
    // First split all lines by %0A
    const allLines = description.split('%0A');

    if (allLines.length < 2) {
      // If can't parse as structured data, return raw content
      return { rawContent: description };
    }

    // First line is header row
    const headerLine = allLines[0];

    // Check if this looks like a structured format
    if (!headerLine.includes('||NO.||') && !headerLine.includes('||')) {
      // If doesn't look like structured data, return raw content
      return { rawContent: description };
    }

    const headers = headerLine.replace('||NO.||', '').replace(/^\|\||\|\|$/g, '').split('||').filter(h => h);

    // Handle multiple items data
    if (servicePackage.allowMultiple) {
      const items: ServiceItem[] = [];

      // Data rows start from the second line
      for (let i = 1; i < allLines.length; i++) {
        const line = allLines[i];
        if (!line || line.trim() === '') continue;

        const rowData = line.split('|').filter(v => v !== '');

        if (rowData.length > 1) {
          const values = rowData.slice(1); // Skip sequence number
          const itemData: Record<string, any> = {};

          headers.forEach((header, index) => {
            const field = servicePackage.fields.find(f => f.label === header);
            if (field && values[index] !== undefined) {
              itemData[field.name] = values[index];
            }
          });

          items.push({
            id: `item-${Date.now()}-${i}`,
            data: itemData
          });
        }
      }

      // If no items were parsed successfully, return raw content
      if (items.length === 0) {
        return { rawContent: description };
      }

      return items;
    }

    // Handle single item data (backward compatibility)
    if (allLines.length >= 2) {
      const dataLine = allLines[1];
      const values = dataLine.split('|').filter(v => v && v !== '1');
      const result: Record<string, any> = {};

      headers.forEach((header, index) => {
        const field = servicePackage.fields.find(f => f.label === header);
        if (field && values[index]) {
          result[field.name] = values[index];
        }
      });

      // If no fields were parsed successfully, return raw content
      if (Object.keys(result).length === 0) {
        return { rawContent: description };
      }

      return result;
    }

    return { rawContent: description };
  } catch (error) {
    console.error('Error parsing description:', error);
    // Return raw content when parsing fails
    return { rawContent: description };
  }
}

// Generate new service item ID
export function generateServiceItemId(): string {
  return `item-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

// Create empty service item
export function createEmptyServiceItem(servicePackageId: string): ServiceItem {
  const servicePackage = servicePackages.find(sp => sp.id === servicePackageId);
  const data: Record<string, any> = {};

  if (servicePackage) {
    servicePackage.fields.forEach(field => {
      data[field.name] = '';
    });
  }

  return {
    id: generateServiceItemId(),
    data
  };
}