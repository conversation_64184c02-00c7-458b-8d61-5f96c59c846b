import axios from 'axios';

const request = axios.create({
  baseURL: '/api',
  timeout: 10000,
});

request.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

request.interceptors.response.use(
  (response) => response,
  (error) => {
    // Only output detailed error information in development environment or when using mock data
    if (import.meta.env.DEV || import.meta.env.VITE_USE_MOCK_DATA === 'true') {
      console.error('API Error:', error);
    }
    return Promise.reject(error);
  }
);

/**
 * SSE请求选项
 */
export interface SSERequestOptions {
  method?: 'GET' | 'POST';
  headers?: Record<string, string>;
  body?: any;
  signal?: AbortSignal;
  timeout?: number;
}

/**
 * 统一的SSE请求方法
 * 使用与axios相同的认证和基础配置
 */
export const sseRequest = async (
  url: string,
  options: SSERequestOptions = {}
): Promise<Response> => {
  const {
    method = 'GET',
    headers = {},
    body,
    signal,
    timeout = 30000
  } = options;

  // 构建完整URL
  const fullUrl = url.startsWith('http') ? url : `/api${url.startsWith('/') ? url : `/${url}`}`;

  // 准备请求头
  const requestHeaders: Record<string, string> = {
    'Accept': 'text/event-stream',
    'Cache-Control': 'no-cache',
    ...headers
  };

  // 添加认证token
  const token = localStorage.getItem('token');
  if (token) {
    requestHeaders.Authorization = `Bearer ${token}`;
  }

  // 如果有body，设置Content-Type
  if (body) {
    requestHeaders['Content-Type'] = 'application/json';
  }

  // 创建超时控制
  const timeoutController = new AbortController();
  const timeoutId = setTimeout(() => timeoutController.abort(), timeout);

  // 合并信号
  const combinedSignal = signal ?
    AbortSignal.any([signal, timeoutController.signal]) :
    timeoutController.signal;

  try {
    const response = await fetch(fullUrl, {
      method,
      headers: requestHeaders,
      body: body ? JSON.stringify(body) : undefined,
      signal: combinedSignal,
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return response;
  } catch (error) {
    clearTimeout(timeoutId);

    // 开发环境下或使用 mock 数据时输出错误信息
    if (import.meta.env.DEV || import.meta.env.VITE_USE_MOCK_DATA === 'true') {
      console.error('SSE Request Error:', error);
    }

    throw error;
  }
};

export default request;