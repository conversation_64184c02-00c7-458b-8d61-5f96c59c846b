/**
 * 颜色常量定义
 * 与 tailwind.config.js 保持一致，避免重复定义
 */

// 主色调系统
export const primaryColors = {
  50: '#eff6ff',
  100: '#dbeafe',
  200: '#bfdbfe',
  300: '#93c5fd',
  400: '#60a5fa',
  500: '#3b82f6',
  600: '#2563eb',
  700: '#1d4ed8',
  800: '#1e40af',
  900: '#1e3a8a',
} as const;

// 深色主题颜色系统
export const darkColors = {
  // 背景色系统
  bg: '#1f2937',           // 主背景
  bgSecondary: '#111827',  // 次要背景
  surface: '#374151',      // 卡片/面板背景
  surfaceHover: '#4b5563', // 悬停状态

  // 边框色系统
  border: '#4b5563',       // 主边框
  borderLight: '#6b7280',  // 浅边框

  // 文字色系统
  text: '#f9fafb',         // 主文字
  textSecondary: '#e5e7eb', // 次要文字
  textMuted: '#d1d5db',    // 弱化文字
  textDisabled: '#9ca3af', // 禁用文字

  // 状态色
  accent: '#3b82f6',       // 强调色
  success: '#10b981',      // 成功色
  warning: '#f59e0b',      // 警告色
  error: '#ef4444',        // 错误色
} as const;

// 导出统一的颜色配置，供 Naive UI 主题使用
export const getThemeColors = () => ({
  primary: primaryColors,
  dark: darkColors,
});

// 导出 Naive UI 主题覆盖配置
export const getNaiveUIThemeOverrides = (isDark: boolean) => {
  const baseOverrides = {
    common: {
      primaryColor: primaryColors[600],
      primaryColorHover: primaryColors[500],
      primaryColorPressed: primaryColors[700],
      borderRadius: '6px'
    }
  };

  if (isDark) {
    return {
      ...baseOverrides,
      common: {
        ...baseOverrides.common,
        bodyColor: darkColors.bg,
        cardColor: darkColors.bg,
        modalColor: darkColors.surface,
        popoverColor: darkColors.surface,
        tableHeaderColor: darkColors.surface,
        tableColor: darkColors.surface,
        inputColor: darkColors.surface,
        codeColor: darkColors.surface,
        tagColor: darkColors.surface,
        avatarColor: darkColors.surface,
        invertedColor: darkColors.surface,
        baseColor: darkColors.bg,
        dividerColor: darkColors.border,
        borderColor: darkColors.border,
        tableColorHover: darkColors.surfaceHover,
        tableColorStriped: darkColors.surfaceHover,
        hoverColor: darkColors.surfaceHover,
        pressedColor: darkColors.surface,
        opacityDisabled: '0.5',
        textColor1: darkColors.text,
        textColor2: darkColors.textSecondary,
        textColor3: darkColors.textMuted,
        textColorDisabled: darkColors.textDisabled,
        placeholderColor: darkColors.textDisabled,
        placeholderColorDisabled: darkColors.borderLight,
        iconColor: darkColors.textMuted,
        iconColorHover: darkColors.text,
        iconColorPressed: darkColors.textSecondary,
        iconColorDisabled: darkColors.textDisabled
      },
      Form: {
        labelTextColor: darkColors.text,
        labelFontWeight: '500'
      }
    };
  }

  return baseOverrides;
};
