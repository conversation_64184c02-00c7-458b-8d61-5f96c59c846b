

import { FormItemValue } from '@/types/form'



export const findParentId = (options: any[], target: string, parent: string | null = null): string | null => {
    for (const item of options) {
    if (item.value === target) {
      return parent
    }
    if (item.children) {
      const res = findParentId(item.children, target, item.value)
      if (res !== null) return res
    }
  }
  return null
}




// [{ x: 1, y: 2, z: 3}]
// ||NO.||x||y||z||%0A|1|1|2|3|%0A
export const stringifyDescription = (list: any[], options: FormItemValue[]) => {

    let description = ''

    list.forEach((item: any, index: number) => {
        if (!description) {
            const headers = Object.keys(item).map((header: string) => {
                const itemOption = options.find((sub: any) => header === sub.modelKey)
                return itemOption ? itemOption.label : header
            })
            description += '||NO.||' + headers.join('||') + '||%0A'
        }

        const content = Object.values(item)
        description += `|${index + 1}|` + content.join('|') + '|%0A'
    });

    return description
}


// ||NO.||x||y||z||%0A|1|1|2|3|%0A
// [{ x: 1, y: 2, z: 3}]
export const parseDescription = (str: string, options: FormItemValue[]) => {
    //  

    const rows = str.split('%0A')

    const headers = parseArray(rows[0]).map((header: string) => {
        const itemOption = options.find((sub: any) => header === sub.label)

        return itemOption ? itemOption.modelKey : header
    })
    const content = rows.slice(1).filter(Boolean)

    const result: any[] = []
    content.forEach((item: string) => {
        const list = parseArray(item)
        const current: Record<string, any> = {}
        list.forEach((sub: string, index: number) => {
            current[headers[index]] = sub
        })
        result.push(current)
    })

    return result
}


const escapeRegExp = (str: string) => str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')

const parseArray = (str: string) => {

    const delimiter =  str.includes('||') ? '||' : '|'
    const escaped = escapeRegExp(delimiter)

    // 去掉首尾分隔符
    const regexp = new RegExp(`^(${escaped})+|(${escaped})+$`, 'g')
    const trimmed = str.replace(regexp, '')

    const list = trimmed.split(delimiter).filter(Boolean)
    list.shift()
    
    return list
}