import { createApp } from 'vue';
import { createPinia } from 'pinia';
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate';
import router from './router';
import App from './App.vue';
import './style.css';

// Naive UI global setup
import {
  create,
  NMessageProvider,
  NConfigProvider
} from 'naive-ui';

const naive = create({
  components: [NMessageProvider, NConfigProvider]
});

const app = createApp(App);

const pinia = createPinia();
pinia.use(piniaPluginPersistedstate);
app.use(pinia);

// 初始化设置
import { useUIStore } from '@/stores/ui';
const uiStore = useUIStore();
uiStore.initializeSettings();

app.use(router);
app.use(naive);

// 错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue Error:', err, info);
};

// 全局未捕获的Promise错误处理
window.addEventListener('unhandledrejection', event => {
  console.error('Unhandled Promise Rejection:', event.reason);
  event.preventDefault();
});

app.mount('#app');

// 开发环境下或使用 mock 数据时在控制台暴露一些调试功能
if (import.meta.env.DEV || import.meta.env.VITE_USE_MOCK_DATA === 'true') {
  import('./stores/ticket').then(({ useTicketStore }) => {
    const ticketStore = useTicketStore();

    // 在全局对象上暴露清除缓存的功能
    (window as any).clearTicketCache = () => {
      ticketStore.clearCache();
      console.log('票据缓存已清除，下次访问时会重新加载数据');
    };

    console.log('开发模式调试功能：');
    console.log('- clearTicketCache() : 清除票据缓存');
  });
}