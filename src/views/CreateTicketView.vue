<template>
  <div class="h-full w-full overflow-y-auto bg-gray-50 dark:bg-dark-bg">
    <div class="p-6 max-w-7xl mx-auto">
      <!-- Header Section -->
      <div class="mb-6">
        <div class="flex items-center mb-4">
          <div class="w-10 h-10 bg-blue-100 dark:bg-dark-accent/20 rounded-lg flex items-center justify-center mr-4">
            <n-icon size="20" color="#3b82f6" class="dark:text-dark-accent">
              <AddCircleOutline />
            </n-icon>
          </div>
          <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-dark-text mb-1">Create New Ticket</h1>
          </div>
        </div>
      </div>

      <!-- Progress Indicator -->
      <div class="mb-6">
        <n-card class="border border-gray-200">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <div class="flex items-center">
                <div class="w-7 h-7 bg-blue-600 rounded-full flex items-center justify-center text-white text-xs font-medium">
                  1
                </div>
                <span class="ml-2 text-sm font-medium text-gray-900 dark:text-dark-text">Basic Info</span>
              </div>
              <div class="w-12 h-0.5 bg-gray-300 dark:bg-dark-border"></div>
              <div class="flex items-center">
                <div class="w-7 h-7 bg-gray-300 dark:bg-dark-border rounded-full flex items-center justify-center text-gray-600 dark:text-dark-text-muted text-xs font-medium">
                  2
                </div>
                <span class="ml-2 text-sm font-medium text-gray-500 dark:text-dark-text-secondary">Service Details</span>
              </div>
              <div class="w-12 h-0.5 bg-gray-300 dark:bg-dark-border"></div>
              <div class="flex items-center">
                <div class="w-7 h-7 bg-gray-300 dark:bg-dark-border rounded-full flex items-center justify-center text-gray-600 dark:text-dark-text-muted text-xs font-medium">
                  3
                </div>
                <span class="ml-2 text-sm font-medium text-gray-500 dark:text-dark-text-secondary">Confirm Submit</span>
              </div>
            </div>
          </div>
        </n-card>
      </div>

      <div>
        <!-- Basic Information Section -->
        <n-card class="mb-6 border border-gray-200">
          <template #header>
            <div class="flex items-center">
              <div class="w-7 h-7 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                <n-icon size="16" color="#3b82f6">
                  <InformationCircleOutline />
                </n-icon>
              </div>
              <span class="text-lg font-semibold text-gray-900 dark:text-dark-text">Basic Information</span>
            </div>
          </template>
          
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-x-8 gap-y-6">
            <div class="space-y-2">
              <div class="text-sm font-medium text-gray-700 dark:text-dark-text">Summary</div>
              <n-input
                v-model:value="formData.summary"
                placeholder="Please enter ticket title"
                size="small"
                class="text-left"
              />
            </div>

            <div class="space-y-2">
              <div class="text-sm font-medium text-gray-700 dark:text-dark-text">Priority</div>
              <n-select
                v-model:value="formData.priority"
                :options="priorityOptions"
                placeholder="Select priority"
                size="small"
              />
            </div>

            <div class="space-y-2">
              <div class="text-sm font-medium text-gray-700 dark:text-dark-text">Components</div>
              <n-cascader
                v-model:value="formData.component"
                :options="locationOptions"
                placeholder="Select location"
                size="small"
                filterable
                expand-trigger="hover"
                class="max-w-xl"
                :show-path="false"
              />
            </div>

            <div class="space-y-2">
              <div class="text-sm font-medium text-gray-700 dark:text-dark-text">Service Packages</div>
              <n-select
                v-model:value="formData.service_package"
                :options="servicePackageOptions"
                placeholder="Select service type"
                size="small"
                @update:value="onServicePackageChange"
              />
            </div>
          </div>
        </n-card>

        <!-- Dynamic Service Fields Section -->
        <n-card 
          v-if="selectedServicePackage" 
          class="mb-6 border border-gray-200"
        >
          <template #header>
            <div class="flex items-center">
              <div class="w-7 h-7 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                <n-icon size="16" color="#10b981">
                  <SettingsOutline />
                </n-icon>
              </div>
              <span class="text-lg font-semibold text-gray-900 dark:text-dark-text">Service Details</span>
            </div>
          </template>
          
          <!-- 多项服务详情表格 -->
          <ServiceItemsTable
            v-if="selectedServicePackage?.allowMultiple"
            :service-package="selectedServicePackage"
            v-model="formData.serviceItems"
            @change="onServiceItemsChange"
          />

          <!-- 单项服务详情（向后兼容） -->
          <div
            v-else-if="selectedServicePackage && !selectedServicePackage.allowMultiple"
            class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-x-6 gap-y-4"
          >
            <div
              v-for="field in selectedServicePackage.fields"
              :key="field.name"
              class="bg-gray-50 dark:bg-dark-surface p-3 rounded border border-gray-100 dark:border-dark-border space-y-2"
            >
              <div class="text-sm font-medium text-gray-700 dark:text-dark-text">
                {{ field.label }}
                <span v-if="field.required" class="text-red-500 ml-1">*</span>
              </div>
              <n-input
                v-if="field.type === 'input'"
                v-model:value="formData.serviceFields[field.name]"
                :placeholder="field.placeholder || `Please enter ${field.label}`"
                size="small"
                class="text-left"
              />
              <n-input
                v-else-if="field.type === 'textarea'"
                v-model:value="formData.serviceFields[field.name]"
                :placeholder="field.placeholder || `Please enter ${field.label}`"
                type="textarea"
                :autosize="{ minRows: 2, maxRows: 4 }"
                size="small"
                class="text-left"
              />
              <n-select
                v-else-if="field.type === 'select'"
                v-model:value="formData.serviceFields[field.name]"
                :options="field.options?.map(opt => ({ label: opt, value: opt }))"
                :placeholder="field.placeholder || `Please select ${field.label}`"
                size="small"
              />
            </div>
          </div>
        </n-card>

        <!-- Action Buttons -->
        <n-card class="border border-gray-200">
          <div class="flex justify-between items-center">
            <div class="text-sm text-gray-500 dark:text-dark-text-secondary">
              <n-icon size="16" class="mr-1">
                <InformationCircleOutline />
              </n-icon>
              Please ensure all required information is filled correctly
            </div>
            <div class="flex space-x-3">
              <n-button 
                size="small" 
                @click="resetForm"
                class="px-4"
              >
                <template #icon>
                  <n-icon>
                    <RefreshOutline />
                  </n-icon>
                </template>
                Reset Form
              </n-button>
              
              <!-- AI建议按钮 -->
              <n-button
                size="small"
                @click="openAIPreassign"
                class="px-4 bg-gradient-to-r from-indigo-600 to-indigo-700 hover:from-indigo-700 hover:to-indigo-800 dark:from-indigo-500 dark:to-indigo-600 dark:hover:from-indigo-600 dark:hover:to-indigo-700 text-white"
                type="primary"
              >
                <template #icon>
                  <n-icon>
                    <SparklesOutline />
                  </n-icon>
                </template>
                AI Preassign
              </n-button>

              <n-button
                type="primary"
                size="small"
                @click="submitForm"
                :loading="loading"
                class="px-4 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 dark:from-blue-500 dark:to-blue-600 dark:hover:from-blue-600 dark:hover:to-blue-700"
              >
                <template #icon>
                  <n-icon>
                    <CheckmarkCircleOutline />
                  </n-icon>
                </template>
                Create Ticket
              </n-button>
            </div>
          </div>
        </n-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, nextTick, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import {
  NInput,
  NSelect,
  NCascader,
  NButton,
  NCard,
  NIcon,
  useMessage
} from 'naive-ui';
import {
  AddCircleOutline,
  InformationCircleOutline,
  SettingsOutline,
  RefreshOutline,
  CheckmarkCircleOutline,
  SparklesOutline
} from '@vicons/ionicons5';
import { storeToRefs } from 'pinia';
import { useTicketStore } from '@/stores/ticket';
import { useUserStore } from '@/stores/user';
import { useUIStore } from '@/stores/ui';
import { useChatStore } from '@/stores/chat';
import { servicePackages, serializeFormData, createEmptyServiceItem } from '@/utils/servicePackages';
import type { Ticket, ServiceItem } from '@/types/ticket';
import { LOCATION_OPTIONS } from '@/types/ticket';
import ServiceItemsTable from '@/components/ServiceItemsTable.vue';

const router = useRouter();
const message = useMessage();
const ticketStore = useTicketStore();
const userStore = useUserStore();
const uiStore = useUIStore();
const chatStore = useChatStore();

const { loading } = storeToRefs(ticketStore);

// 从UI store获取默认值
const defaults = uiStore.getTicketCreationDefaults();

const formData = reactive({
  summary: '',
  priority: defaults.priority || 'Medium' as Ticket['priority'],
  service_package: defaults.service_package || '',
  component: defaults.component || '', // Location information
  serviceFields: {} as Record<string, any>, // Single service fields
  serviceItems: [] as ServiceItem[] // Multiple service data
});

const priorityOptions = [
  { label: 'Minor', value: 'Minor' },
  { label: 'Low', value: 'Low' },
  { label: 'Medium', value: 'Medium' },
  { label: 'High', value: 'High' },
  { label: 'Critical', value: 'Critical' }
];

const servicePackageOptions = servicePackages.map(sp => ({
  label: sp.name,
  value: sp.id
}));

// Location options - directly use two-level menu structure
const locationOptions = LOCATION_OPTIONS;

const selectedServicePackage = computed(() => {
  return servicePackages.find(sp => sp.id === formData.service_package);
});

const onServicePackageChange = () => {
  // Reset service fields when service package changes
  formData.serviceFields = {};
  formData.serviceItems = [];

  // If it's a multi-item service, automatically add the first item
  if (selectedServicePackage.value?.allowMultiple) {
    formData.serviceItems = [createEmptyServiceItem(formData.service_package)];
  }
};

// Handle service item changes
const onServiceItemsChange = () => {
  // Clear previous validation errors when data changes
  // Note: This component uses manual validation instead of n-form
  // No additional action needed here as validation is handled in submitForm
};

// Initialize service package related data when component is mounted
onMounted(() => {
  // If there's a default service_package, initialize related data
  if (formData.service_package) {
    onServicePackageChange();
  }
});

const resetForm = () => {
  const currentDefaults = uiStore.getTicketCreationDefaults();
  formData.summary = '';
  formData.priority = currentDefaults.priority || 'Medium';
  formData.service_package = currentDefaults.service_package || '';
  formData.component = currentDefaults.component || '';
  formData.serviceFields = {};
  formData.serviceItems = [];

  // If there's a default service_package, reinitialize related data
  if (formData.service_package) {
    nextTick(() => {
      onServicePackageChange();
    });
  }
};

// AI preassign feature
const openAIPreassign = async () => {
  try {
    // 使用手动验证替代 n-form 验证
    if (!validateForm()) {
      return;
    }

    // Select data source based on service package type
    const serviceData = selectedServicePackage.value?.allowMultiple
      ? formData.serviceItems
      : formData.serviceFields;

    const description = serializeFormData(formData.service_package, serviceData);

    const ticketData: Partial<Ticket> = {
      summary: formData.summary,
      priority: formData.priority,
      service_package: formData.service_package,
      component: formData.component, // Location information
      description,
      status: 'Open',
      reporter_email: userStore.getCurrentUserEmail() // 使用当前登录用户邮箱
    };

    // 自动展开 AI 面板（如果当前是隐藏状态）
    if (!uiStore.isAIPanelVisible) {
      uiStore.aiPanel.show();
      if (import.meta.env.DEV) {
        console.log('Auto-showing AI panel for AI Preassign');
      }
    }

    // 发送ticket_preassign命令到chatbot
    await chatStore.sendCommand('ticket_preassign', ticketData);

    // Show success message
    message.success('AI Preassign request sent to chatbot, please check the right panel');
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error('Error opening AI preassign:', error);
    }

    // 处理表单验证错误
    if (error && typeof error === 'object' && Array.isArray(error)) {
      // This is Naive UI validation error
      const validationErrors = error.flat();
      if (validationErrors.length > 0 && validationErrors[0].message) {
        message.warning('Please check error items in the form');
        return;
      }
    }

    message.error('AI preassign feature is temporarily unavailable, please try again later');
  }
};

// 手动验证函数
const validateForm = () => {
  // 验证基本字段
  if (!formData.summary?.trim()) {
    message.error('Please enter ticket title');
    return false;
  }
  if (!formData.priority) {
    message.error('Please select priority');
    return false;
  }
  if (!formData.service_package) {
    message.error('Please select service type');
    return false;
  }
  if (!formData.component) {
    message.error('Please select location');
    return false;
  }

  // 验证服务项目
  if (selectedServicePackage.value?.allowMultiple) {
    if (!formData.serviceItems || formData.serviceItems.length === 0) {
      message.error(`Please add at least one ${selectedServicePackage.value.itemName || 'item'}`);
      return false;
    }

    // 验证每个项目的必填字段
    for (let i = 0; i < formData.serviceItems.length; i++) {
      const item = formData.serviceItems[i];
      for (const field of selectedServicePackage.value.fields) {
        if (field.required) {
          const fieldValue = item.data[field.name];
          if (!fieldValue ||
              (typeof fieldValue === 'string' && fieldValue.trim() === '') ||
              (Array.isArray(fieldValue) && fieldValue.length === 0)) {
            message.error(`${field.label} of ${selectedServicePackage.value.itemName || 'item'} ${i + 1} cannot be empty`);
            return false;
          }
        }
      }
    }
  } else if (selectedServicePackage.value) {
    // 验证单项服务的必填字段
    for (const field of selectedServicePackage.value.fields) {
      if (field.required) {
        const fieldValue = formData.serviceFields[field.name];
        if (!fieldValue ||
            (typeof fieldValue === 'string' && fieldValue.trim() === '') ||
            (Array.isArray(fieldValue) && fieldValue.length === 0)) {
          message.error(`Please fill in ${field.label}`);
          return false;
        }
      }
    }
  }

  return true;
};

const submitForm = async () => {
  try {
    // 使用手动验证替代 n-form 验证
    if (!validateForm()) {
      return;
    }

    // Select data source based on service package type
    const serviceData = selectedServicePackage.value?.allowMultiple
      ? formData.serviceItems
      : formData.serviceFields;

    const description = serializeFormData(formData.service_package, serviceData);
    
    const ticketData: Partial<Ticket> = {
      summary: formData.summary,
      priority: formData.priority,
      service_package: formData.service_package,
      component: formData.component, // Location information
      description,
      status: 'Open',
      reporter_email: userStore.getCurrentUserEmail() // 使用当前登录用户邮箱
    };

    // Debug output: print serialized ticket information
    console.log('=== Create Ticket Debug Info ===');
    console.log('Form data (formData):', JSON.stringify(formData, null, 2));
    // console.log('Service data (serviceData):', JSON.stringify(serviceData, null, 2));
    //console.log('Serialized description (description):', description);
    console.log('Final ticket data (ticketData):', JSON.stringify(ticketData, null, 2));
    console.log('Selected service package (selectedServicePackage):', JSON.stringify(selectedServicePackage.value, null, 2));

    const newTicket = await ticketStore.createTicket(ticketData);

    message.success('Ticket created successfully!');

    // Save current Priority, Component and ServicePackage settings for next use
    uiStore.updateTicketCreationDefaults({
      priority: formData.priority,
      component: formData.component,
      service_package: formData.service_package
    });

    // Reset form
    resetForm();

    // Ensure ticket list is shown
    uiStore.ticketList.show();

    // Ensure ticket data is loaded, then navigate to "Created by Me" list
    await ticketStore.loadTickets();
    router.push('/tickets/created');
    
    if (import.meta.env.DEV) {
      console.log('New ticket created:', newTicket.issues_key);
    }
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error('Error creating ticket:', error);
    }
    
    // Improved form validation error handling
    if (error && typeof error === 'object' && Array.isArray(error)) {
      // This is Naive UI validation error
      const validationErrors = error.flat();
      if (validationErrors.length > 0 && validationErrors[0].message) {
        message.warning('Please check error items in the form');
        return;
      }
    }
    
    // Other types of errors
    message.error('Failed to create ticket, please try again');
  }
};
</script>

<style scoped>
/* Ensure text in input boxes is left-aligned */
:deep(.n-input__input-el) {
  text-align: left !important;
}

:deep(.n-input__textarea-el) {
  text-align: left !important;
}

/* Ensure text in select boxes is left-aligned */
:deep(.n-base-selection-input) {
  text-align: left !important;
}

:deep(.n-base-selection-placeholder) {
  text-align: left !important;
}
</style>