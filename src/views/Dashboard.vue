<template>
  <div class="h-full w-full overflow-y-auto bg-gray-50 dark:bg-dark-bg">
    <div class="p-6">
      <!-- Header -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-dark-text mb-2">Ticket Statistics Dashboard</h1>
        <p class="text-gray-600 dark:text-dark-text-secondary">View your ticket statistics and trend analysis</p>
      </div>

      <!-- Loading State -->
      <div v-if="loading" class="flex items-center justify-center h-64">
        <n-spin size="large" />
      </div>

      <!-- Dashboard Content -->
      <div v-else class="space-y-8">
        <!-- Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <!-- 我创建的工单总数 -->
          <n-card class="shadow-sm hover:shadow-md transition-shadow duration-200">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <n-icon size="24" color="#3b82f6">
                    <PersonOutline />
                  </n-icon>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-600 dark:text-dark-text-secondary">Created by Me</p>
                <p class="text-2xl font-bold text-gray-900 dark:text-dark-text">{{ createdTicketsStats.total }}</p>
              </div>
            </div>
          </n-card>

          <!-- 我处理的工单总数 -->
          <n-card class="shadow-sm hover:shadow-md transition-shadow duration-200">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <n-icon size="24" color="#10b981">
                    <DocumentsOutline />
                  </n-icon>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-600 dark:text-dark-text-secondary">My Assigned Tickets</p>
                <p class="text-2xl font-bold text-gray-900 dark:text-dark-text">{{ assignedTicketsStats.total }}</p>
              </div>
            </div>
          </n-card>

          <!-- 待处理工单 -->
          <n-card class="shadow-sm hover:shadow-md transition-shadow duration-200">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                  <n-icon size="24" color="#f59e0b">
                    <TimeOutline />
                  </n-icon>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-600 dark:text-dark-text-secondary">Pending Tickets</p>
                <p class="text-2xl font-bold text-gray-900 dark:text-dark-text">{{ pendingTicketsCount }}</p>
              </div>
            </div>
          </n-card>

          <!-- 已完成工单 -->
          <n-card class="shadow-sm hover:shadow-md transition-shadow duration-200">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center">
                  <n-icon size="24" color="#059669">
                    <CheckmarkCircleOutline />
                  </n-icon>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-600 dark:text-dark-text-secondary">Completed Tickets</p>
                <p class="text-2xl font-bold text-gray-900 dark:text-dark-text">{{ completedTicketsCount }}</p>
              </div>
            </div>
          </n-card>
        </div>

        <!-- Work Efficiency Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <!-- 总工作时长 -->
          <n-card class="shadow-sm hover:shadow-md transition-shadow duration-200">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <n-icon size="24" color="#8b5cf6">
                    <TimerOutline />
                  </n-icon>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-600 dark:text-dark-text-secondary">Total Work Hours</p>
                <p class="text-2xl font-bold text-gray-900 dark:text-dark-text">{{ workEfficiencyStats.totalWorkHours }}h</p>
              </div>
            </div>
          </n-card>

          <!-- 平均处理时间 -->
          <n-card class="shadow-sm hover:shadow-md transition-shadow duration-200">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
                  <n-icon size="24" color="#6366f1">
                    <SpeedometerOutline />
                  </n-icon>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-600 dark:text-dark-text-secondary">Avg Resolution Time</p>
                <p class="text-2xl font-bold text-gray-900 dark:text-dark-text">{{ workEfficiencyStats.avgResolutionTime }}</p>
              </div>
            </div>
          </n-card>

          <!-- SLA遵守率 -->
          <n-card class="shadow-sm hover:shadow-md transition-shadow duration-200">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center">
                  <n-icon size="24" color="#14b8a6">
                    <ShieldCheckmarkOutline />
                  </n-icon>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-600 dark:text-dark-text-secondary">SLA Compliance</p>
                <p class="text-2xl font-bold text-gray-900 dark:text-dark-text">{{ workEfficiencyStats.slaComplianceRate }}%</p>
              </div>
            </div>
          </n-card>

          <!-- 工作日志条目数 -->
          <n-card class="shadow-sm hover:shadow-md transition-shadow duration-200">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-cyan-100 rounded-lg flex items-center justify-center">
                  <n-icon size="24" color="#06b6d4">
                    <DocumentTextOutline />
                  </n-icon>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-600 dark:text-dark-text-secondary">Work Log Entries</p>
                <p class="text-2xl font-bold text-gray-900 dark:text-dark-text">{{ workEfficiencyStats.totalWorklogEntries }}</p>
              </div>
            </div>
          </n-card>
        </div>

        <!-- Charts Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <!-- 我创建的工单状态分布 -->
          <n-card title="Created by Me Status Distribution" class="shadow-sm">
            <template #header-extra>
              <n-tag type="info" size="small">{{ createdTicketsStats.total }} tickets</n-tag>
            </template>
            <div class="h-80">
              <v-chart 
                v-if="createdTicketsStats.total > 0"
                :option="createdTicketsPieOption" 
                autoresize 
                class="w-full h-full"
                ref="createdPieChart"
              />
              <div v-else class="flex items-center justify-center h-full text-gray-500 dark:text-dark-text-secondary">
                No data
              </div>
            </div>
          </n-card>

          <!-- 我处理的工单状态分布 -->
          <n-card title="My Assigned Tickets Status Distribution" class="shadow-sm">
            <template #header-extra>
              <n-tag type="success" size="small">{{ assignedTicketsStats.total }} tickets</n-tag>
            </template>
            <div class="h-80">
              <v-chart 
                v-if="assignedTicketsStats.total > 0"
                :option="assignedTicketsPieOption" 
                autoresize 
                class="w-full h-full"
                ref="assignedPieChart"
              />
              <div v-else class="flex items-center justify-center h-full text-gray-500 dark:text-dark-text-secondary">
                No data
              </div>
            </div>
          </n-card>
        </div>

        <!-- Priority Distribution -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <!-- 我创建的工单优先级分布 -->
          <n-card title="Created by Me Priority Distribution" class="shadow-sm">
            <div class="h-64">
              <v-chart 
                v-if="createdTicketsStats.total > 0"
                :option="createdPriorityBarOption" 
                autoresize 
                class="w-full h-full"
                ref="createdBarChart"
              />
              <div v-else class="flex items-center justify-center h-full text-gray-500 dark:text-dark-text-secondary">
                No data
              </div>
            </div>
          </n-card>

          <!-- 我处理的工单优先级分布 -->
          <n-card title="My Assigned Tickets Priority Distribution" class="shadow-sm">
            <div class="h-64">
              <v-chart 
                v-if="assignedTicketsStats.total > 0"
                :option="assignedPriorityBarOption" 
                autoresize 
                class="w-full h-full"
                ref="assignedBarChart"
              />
              <div v-else class="flex items-center justify-center h-full text-gray-500 dark:text-dark-text-secondary">
                No data
              </div>
            </div>
          </n-card>
        </div>

        <!-- Time Trend Analysis -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <!-- 工单创建趋势 -->
          <n-card title="Ticket Creation Trend (Last 30 Days)" class="shadow-sm">
            <div class="h-64">
              <v-chart
                v-if="timeTrendStats.creationTrend.length > 0"
                :option="creationTrendOption"
                autoresize
                class="w-full h-full"
                ref="creationTrendChart"
              />
              <div v-else class="flex items-center justify-center h-full text-gray-500 dark:text-dark-text-secondary">
                No data
              </div>
            </div>
          </n-card>

          <!-- 工单解决趋势 -->
          <n-card title="Ticket Resolution Trend (Last 30 Days)" class="shadow-sm">
            <div class="h-64">
              <v-chart
                v-if="timeTrendStats.resolutionTrend.length > 0"
                :option="resolutionTrendOption"
                autoresize
                class="w-full h-full"
                ref="resolutionTrendChart"
              />
              <div v-else class="flex items-center justify-center h-full text-gray-500 dark:text-dark-text-secondary">
                No data
              </div>
            </div>
          </n-card>
        </div>

        <!-- Service Package Distribution -->
        <n-card title="Service Type Distribution" class="shadow-sm">
          <div class="h-80">
            <v-chart
              v-if="(createdTicketsStats.total + assignedTicketsStats.total) > 0"
              :option="servicePackageOption"
              autoresize
              class="w-full h-full"
              ref="serviceChart"
            />
            <div v-else class="flex items-center justify-center h-full text-gray-500 dark:text-dark-text-secondary">
              No data
            </div>
          </div>
        </n-card>

        <!-- Recent Activity -->
        <n-card title="Recent Activity" class="shadow-sm">
          <div class="space-y-4">
            <div
              v-for="activity in recentActivities"
              :key="activity.id"
              class="flex items-center p-4 bg-gray-50 dark:bg-dark-surface rounded-lg"
            >
              <div class="flex-shrink-0">
                <n-icon
                  :size="20"
                  :color="getActivityColor(activity.type)"
                  class="drop-shadow-sm"
                >
                  <component :is="getActivityIcon(activity.type)" />
                </n-icon>
              </div>
              <div class="ml-4 flex-1">
                <p class="text-sm font-medium text-gray-900 dark:text-dark-text">{{ activity.title }}</p>
                <p class="text-xs text-gray-500 dark:text-dark-text-secondary">{{ activity.description }}</p>
              </div>
              <div class="text-xs text-gray-400 dark:text-dark-text-muted">
                {{ formatRelativeTime(activity.timestamp) }}
              </div>
            </div>
            <div v-if="recentActivities.length === 0" class="text-center py-8 text-gray-500 dark:text-dark-text-secondary">
              No recent activity
            </div>
          </div>
        </n-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue';
import { NCard, NIcon, NSpin, NTag } from 'naive-ui';
import {
  PersonOutline,
  DocumentsOutline,
  TimeOutline,
  CheckmarkCircleOutline,
  PlayCircleOutline,
  StopCircleOutline,
  TimerOutline,
  SpeedometerOutline,
  ShieldCheckmarkOutline,
  DocumentTextOutline
} from '@vicons/ionicons5';
import { storeToRefs } from 'pinia';
import { useTicketStore } from '@/stores/ticket';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import VChart from 'vue-echarts';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { PieChart, BarChart, LineChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components';

// Register ECharts components
use([
  CanvasRenderer,
  PieChart,
  BarChart,
  LineChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
]);

const ticketStore = useTicketStore();
const { loading, createdTickets, assignedTickets } = storeToRefs(ticketStore);

// Chart refs for proper cleanup
const createdPieChart = ref();
const assignedPieChart = ref();
const createdBarChart = ref();
const assignedBarChart = ref();
const serviceChart = ref();
const creationTrendChart = ref();
const resolutionTrendChart = ref();

// Fetch all ticket data
const fetchAllTicketsData = async () => {
  try {
    // Load all tickets at once
    await ticketStore.loadTickets();
  } catch (error) {
    console.error('Error loading tickets data:', error);
  }
};

// Created by Me statistics
const createdTicketsStats = computed(() => {
  const tickets = createdTickets.value;
  const statusCounts = tickets.reduce((acc, ticket) => {
    acc[ticket.status] = (acc[ticket.status] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const priorityCounts = tickets.reduce((acc, ticket) => {
    acc[ticket.priority] = (acc[ticket.priority] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return {
    total: tickets.length,
    statusCounts,
    priorityCounts
  };
});

// My assigned tickets statistics
const assignedTicketsStats = computed(() => {
  const tickets = assignedTickets.value;
  const statusCounts = tickets.reduce((acc, ticket) => {
    acc[ticket.status] = (acc[ticket.status] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const priorityCounts = tickets.reduce((acc, ticket) => {
    acc[ticket.priority] = (acc[ticket.priority] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return {
    total: tickets.length,
    statusCounts,
    priorityCounts
  };
});

// Pending tickets count
const pendingTicketsCount = computed(() => {
  const allTickets = [...createdTickets.value, ...assignedTickets.value];
  return allTickets.filter(ticket => 
    ticket.status === 'Open' || ticket.status === 'In Progress'
  ).length;
});

// Completed tickets count
const completedTicketsCount = computed(() => {
  const allTickets = [...createdTickets.value, ...assignedTickets.value];
  return allTickets.filter(ticket =>
    ticket.status === 'Resolved' || ticket.status === 'Closed'
  ).length;
});

// Work efficiency statistics
const workEfficiencyStats = computed(() => {
  const myAssignedTickets = assignedTickets.value;

  // 计算总工作时长
  const totalWorkHours = myAssignedTickets.reduce((total, ticket) => {
    const ticketWorkTime = ticket.worklogs?.reduce((sum, log) =>
      sum + (log.worklog_timeSpent || 0), 0) || 0;
    return total + ticketWorkTime;
  }, 0);

  // 计算平均处理时间（已解决的工单）
  const resolvedTickets = myAssignedTickets.filter(ticket =>
    ticket.status === 'Resolved' || ticket.status === 'Closed'
  );

  let avgResolutionTime = 'N/A';
  if (resolvedTickets.length > 0) {
    const totalResolutionHours = resolvedTickets.reduce((total, ticket) => {
      if (ticket.created && ticket.resolved) {
        const createdTime = new Date(ticket.created).getTime();
        const resolvedTime = new Date(ticket.resolved).getTime();
        const hours = (resolvedTime - createdTime) / (1000 * 60 * 60);
        return total + hours;
      }
      return total;
    }, 0);

    const avgHours = totalResolutionHours / resolvedTickets.length;
    if (avgHours < 24) {
      avgResolutionTime = `${avgHours.toFixed(1)}h`;
    } else {
      avgResolutionTime = `${(avgHours / 24).toFixed(1)}d`;
    }
  }

  // 计算SLA遵守率
  const ticketsWithSLAData = myAssignedTickets.filter(ticket =>
    ticket.yes_sla_missed !== undefined
  );
  let slaComplianceRate = 100;
  if (ticketsWithSLAData.length > 0) {
    const slaViolations = ticketsWithSLAData.reduce((sum, ticket) =>
      sum + (ticket.yes_sla_missed || 0), 0);
    slaComplianceRate = Math.round(((ticketsWithSLAData.length - slaViolations) / ticketsWithSLAData.length) * 100);
  }

  // 计算工作日志条目总数
  const totalWorklogEntries = myAssignedTickets.reduce((total, ticket) =>
    total + (ticket.worklogs?.length || 0), 0);

  return {
    totalWorkHours: totalWorkHours.toFixed(1),
    avgResolutionTime,
    slaComplianceRate,
    totalWorklogEntries
  };
});

// Time trend statistics
const timeTrendStats = computed(() => {
  const now = new Date();

  // 创建日期数组（最近30天）
  const dateArray = [];
  for (let i = 29; i >= 0; i--) {
    const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
    dateArray.push(date.toISOString().split('T')[0]);
  }

  // 统计每天创建的工单数量
  const creationTrend = dateArray.map(date => {
    const count = createdTickets.value.filter(ticket => {
      const ticketDate = new Date(ticket.created).toISOString().split('T')[0];
      return ticketDate === date;
    }).length;
    return { date, count };
  });

  // 统计每天解决的工单数量（只统计我处理的）
  const resolutionTrend = dateArray.map(date => {
    const count = assignedTickets.value.filter(ticket => {
      if (!ticket.resolved) return false;
      const resolvedDate = new Date(ticket.resolved).toISOString().split('T')[0];
      return resolvedDate === date;
    }).length;
    return { date, count };
  });

  return {
    creationTrend,
    resolutionTrend
  };
});

// Creation trend chart configuration
const creationTrendOption = computed(() => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: timeTrendStats.value.creationTrend.map(item => {
      const date = new Date(item.date);
      return `${date.getMonth() + 1}/${date.getDate()}`;
    }),
    axisLabel: {
      fontSize: 10
    }
  },
  yAxis: {
    type: 'value',
    minInterval: 1
  },
  series: [
    {
      name: 'Created Tickets',
      type: 'line',
      smooth: true,
      itemStyle: {
        color: '#3b82f6'
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(59, 130, 246, 0.3)' },
            { offset: 1, color: 'rgba(59, 130, 246, 0.1)' }
          ]
        }
      },
      data: timeTrendStats.value.creationTrend.map(item => item.count)
    }
  ]
}));

// Resolution trend chart configuration
const resolutionTrendOption = computed(() => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: timeTrendStats.value.resolutionTrend.map(item => {
      const date = new Date(item.date);
      return `${date.getMonth() + 1}/${date.getDate()}`;
    }),
    axisLabel: {
      fontSize: 10
    }
  },
  yAxis: {
    type: 'value',
    minInterval: 1
  },
  series: [
    {
      name: 'Resolved Tickets',
      type: 'line',
      smooth: true,
      itemStyle: {
        color: '#10b981'
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(16, 185, 129, 0.3)' },
            { offset: 1, color: 'rgba(16, 185, 129, 0.1)' }
          ]
        }
      },
      data: timeTrendStats.value.resolutionTrend.map(item => item.count)
    }
  ]
}));

// Status color mapping
const statusColors = {
  'Open': '#3b82f6',
  'In Progress': '#f59e0b',
  'Resolved': '#10b981',
  'Closed': '#6b7280'
};

// Priority color mapping
const priorityColors = {
  'Critical': '#ef4444',
  'High': '#f97316',
  'Medium': '#3b82f6',
  'Low': '#10b981',
  'Minor': '#9ca3af'
};

// Created by Me pie chart configuration
const createdTicketsPieOption = computed(() => ({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 'left',
    textStyle: {
      fontSize: 12
    }
  },
  series: [
    {
      name: 'Ticket Status',
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['60%', '50%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 8,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: 16,
          fontWeight: 'bold'
        },
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      labelLine: {
        show: false
      },
      data: Object.entries(createdTicketsStats.value.statusCounts).map(([status, count]) => ({
        value: count,
        name: status,
        itemStyle: {
          color: statusColors[status as keyof typeof statusColors]
        }
      }))
    }
  ]
}));

// My assigned tickets pie chart configuration
const assignedTicketsPieOption = computed(() => ({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 'left',
    textStyle: {
      fontSize: 12
    }
  },
  series: [
    {
      name: 'Ticket Status',
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['60%', '50%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 8,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: 16,
          fontWeight: 'bold'
        },
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      labelLine: {
        show: false
      },
      data: Object.entries(assignedTicketsStats.value.statusCounts).map(([status, count]) => ({
        value: count,
        name: status,
        itemStyle: {
          color: statusColors[status as keyof typeof statusColors]
        }
      }))
    }
  ]
}));

// Created by Me priority bar chart configuration
const createdPriorityBarOption = computed(() => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: Object.keys(createdTicketsStats.value.priorityCounts),
    axisTick: {
      alignWithLabel: true
    }
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: 'Ticket Count',
      type: 'bar',
      barWidth: '60%',
      itemStyle: {
        borderRadius: [4, 4, 0, 0]
      },
      data: Object.entries(createdTicketsStats.value.priorityCounts).map(([priority, count]) => ({
        value: count,
        itemStyle: {
          color: priorityColors[priority as keyof typeof priorityColors]
        }
      }))
    }
  ]
}));

// My assigned tickets priority bar chart configuration
const assignedPriorityBarOption = computed(() => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: Object.keys(assignedTicketsStats.value.priorityCounts),
    axisTick: {
      alignWithLabel: true
    }
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: 'Ticket Count',
      type: 'bar',
      barWidth: '60%',
      itemStyle: {
        borderRadius: [4, 4, 0, 0]
      },
      data: Object.entries(assignedTicketsStats.value.priorityCounts).map(([priority, count]) => ({
        value: count,
        itemStyle: {
          color: priorityColors[priority as keyof typeof priorityColors]
        }
      }))
    }
  ]
}));

// Service type distribution
const servicePackageOption = computed(() => {
  const allTickets = [...createdTickets.value, ...assignedTickets.value];
  const servicePackageCounts = allTickets.reduce((acc, ticket) => {
    const serviceName = getServicePackageName(ticket.service_package);
    acc[serviceName] = (acc[serviceName] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: Object.keys(servicePackageCounts),
      axisLabel: {
        fontSize: 11
      }
    },
    series: [
      {
        name: 'Ticket Count',
        type: 'bar',
        itemStyle: {
          borderRadius: [0, 4, 4, 0],
          color: '#3b82f6'
        },
        data: Object.values(servicePackageCounts)
      }
    ]
  };
});

// Recent activities
const recentActivities = computed(() => {
  const allTickets = [...createdTickets.value, ...assignedTickets.value];
  return allTickets
    .sort((a, b) => new Date(b.updated).getTime() - new Date(a.updated).getTime())
    .slice(0, 5)
    .map(ticket => ({
      id: ticket.issues_id,
      type: ticket.status,
      title: `${ticket.issues_key} - ${ticket.summary}`,
      description: `Status: ${ticket.status} | Priority: ${ticket.priority}`,
      timestamp: ticket.updated
    }));
});

// 获取服务包名称
const getServicePackageName = (id: string) => {
  const serviceMap: Record<string, string> = {
    'network-troubleshooting': 'Network Troubleshooting',
    'software-installation': 'Software Installation',
    'asset-transfer': 'Asset Transfer'
  };
  return serviceMap[id] || id;
};

// 获取活动图标
const getActivityIcon = (type: string) => {
  const iconMap: Record<string, any> = {
    'Open': TimeOutline,
    'In Progress': PlayCircleOutline,
    'Resolved': CheckmarkCircleOutline,
    'Closed': StopCircleOutline
  };
  return iconMap[type] || DocumentsOutline;
};

// Get activity color
const getActivityColor = (type: string) => {
  return statusColors[type as keyof typeof statusColors] || '#6b7280';
};

// 格式化相对时间
const formatRelativeTime = (dateString: string) => {
  return formatDistanceToNow(new Date(dateString), { 
    addSuffix: true, 
    locale: zhCN 
  });
};

// Clean up ECharts instances
const cleanupCharts = () => {
  const charts = [
    createdPieChart.value,
    assignedPieChart.value,
    createdBarChart.value,
    assignedBarChart.value,
    serviceChart.value,
    creationTrendChart.value,
    resolutionTrendChart.value
  ];
  
  charts.forEach(chart => {
    if (chart && chart.dispose) {
      try {
        chart.dispose();
      } catch (error) {
        // Ignore charts that have already been destroyed
        if (import.meta.env.DEV) {
          console.warn('Chart already disposed:', error);
        }
      }
    }
  });
};

// Fetch data when component is mounted
onMounted(() => {
  fetchAllTicketsData();
});

// Clean up charts when component is unmounted
onUnmounted(() => {
  cleanupCharts();
});
</script>