<template>
  <div class="h-full w-full overflow-y-auto bg-gray-50 dark:bg-dark-bg">
    <div class="flex items-center justify-center min-h-full p-8">
      <div class="max-w-4xl w-full">
        <!-- 主要内容区域 -->
        <div class="text-center mb-12">
          <!-- Logo 区域 -->
          <div class="mb-8">

            <h1 class="text-4xl font-light text-gray-900 dark:text-dark-text mb-3">Intelligent Ticket Management System</h1>
            <p class="text-lg text-gray-600 dark:text-dark-text-secondary font-light">Efficient and Intelligent Service Platform</p>
          </div>

          <!-- 功能特性卡片 -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            <!-- 工单管理 -->
            <div class="bg-white dark:bg-dark-surface rounded-xl p-6 shadow-sm border border-gray-100 dark:border-dark-border hover:shadow-md transition-shadow duration-300">
              <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mx-auto mb-4">
                <n-icon size="24" color="#3b82f6" class="dark:text-blue-400">
                  <ClipboardOutline />
                </n-icon>
              </div>
              <h3 class="text-lg font-medium text-gray-900 dark:text-dark-text mb-2">Intelligent Ticket Management</h3>
              <p class="text-sm text-gray-600 dark:text-dark-text-secondary leading-relaxed">
                Intelligently manage full lifecycle of tickets, from creation to resolution, with automated assignment and handling
              </p>
            </div>

            <!-- 实时协作 -->
            <div class="bg-white dark:bg-dark-surface rounded-xl p-6 shadow-sm border border-gray-100 dark:border-dark-border hover:shadow-md transition-shadow duration-300">
              <div class="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center mx-auto mb-4">
                <n-icon size="24" color="#10b981" class="dark:text-green-400">
                  <PeopleOutline />
                </n-icon>
              </div>
              <h3 class="text-lg font-medium text-gray-900 dark:text-dark-text mb-2">Team Collaboration</h3>
              <p class="text-sm text-gray-600 dark:text-dark-text-secondary leading-relaxed">
                Real-time status updates, work log tracking, and enhanced team collaboration efficiency
              </p>
            </div>

            <!-- 数据分析 -->
            <div class="bg-white dark:bg-dark-surface rounded-xl p-6 shadow-sm border border-gray-100 dark:border-dark-border hover:shadow-md transition-shadow duration-300">
              <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center mx-auto mb-4">
                <n-icon size="24" color="#8b5cf6" class="dark:text-purple-400">
                  <BarChartOutline />
                </n-icon>
              </div>
              <h3 class="text-lg font-medium text-gray-900 dark:text-dark-text mb-2">Data Insights</h3>
              <p class="text-sm text-gray-600 dark:text-dark-text-secondary leading-relaxed">
                Detailed statistical reports and trend analysis to support decision optimization
              </p>
            </div>
          </div>

          <!-- AI 智能解决方案 -->
          <div class="mb-16">
            <div class="text-center mb-12">
              <h2 class="text-3xl font-light text-gray-900 dark:text-dark-text mb-4">AI-Powered Use Cases</h2>
              <p class="text-lg text-gray-600 dark:text-dark-text-secondary max-w-2xl mx-auto">
                Leverage artificial intelligence to transform your service management experience
              </p>
            </div>

            <div class="space-y-12">
              <!-- Case 1: AI 智能分单 -->
              <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-2xl p-8 border border-blue-100 dark:border-blue-800/30">
                <div class="flex flex-col lg:flex-row items-center gap-8">
                  <div class="lg:w-1/2">
                    <div class="flex items-center mb-4">
                      <div class="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center mr-4">
                        <n-icon size="24" color="white">
                          <BulbOutline />
                        </n-icon>
                      </div>
                      <h3 class="text-2xl font-semibold text-gray-900 dark:text-dark-text">AI Smart Assignment</h3>
                    </div>
                    <p class="text-gray-700 dark:text-dark-text-secondary text-lg leading-relaxed mb-6">
                      Our intelligent system analyzes ticket descriptions, employee profiles, and historical data to automatically assign tickets to the most suitable team members, dramatically improving resolution efficiency and customer satisfaction.
                    </p>
                    <div class="flex flex-wrap gap-2">
                      <span class="px-3 py-1 bg-blue-100 dark:bg-blue-900/40 text-blue-800 dark:text-blue-300 rounded-full text-sm font-medium">Smart Matching</span>
                      <span class="px-3 py-1 bg-blue-100 dark:bg-blue-900/40 text-blue-800 dark:text-blue-300 rounded-full text-sm font-medium">Historical Analysis</span>
                      <span class="px-3 py-1 bg-blue-100 dark:bg-blue-900/40 text-blue-800 dark:text-blue-300 rounded-full text-sm font-medium">Efficiency Boost</span>
                    </div>
                  </div>
                  <div class="lg:w-1/2">
                    <div class="bg-white dark:bg-dark-surface rounded-xl p-6 shadow-lg border border-gray-200 dark:border-dark-border">
                      <img
                        src="https://images.unsplash.com/photo-1677442136019-21780ecad995?w=500&h=300&fit=crop&crop=center"
                        alt="AI Smart Assignment"
                        class="w-full h-48 object-cover rounded-lg mb-4"
                      />
                      <div class="text-center">
                        <div class="text-3xl font-bold text-blue-600 dark:text-blue-400"></div>
                        <div class="text-sm text-gray-600 dark:text-dark-text-secondary">Faster Resolution</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Case 2: 智能工作助手 -->
              <div class="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-2xl p-8 border border-green-100 dark:border-green-800/30">
                <div class="flex flex-col lg:flex-row-reverse items-center gap-8">
                  <div class="lg:w-1/2">
                    <div class="flex items-center mb-4">
                      <div class="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center mr-4">
                        <n-icon size="24" color="white">
                          <TrendingUpOutline />
                        </n-icon>
                      </div>
                      <h3 class="text-2xl font-semibold text-gray-900 dark:text-dark-text">Smart Work Assistant</h3>
                    </div>
                    <p class="text-gray-700 dark:text-dark-text-secondary text-lg leading-relaxed mb-6">
                      Your personal AI assistant analyzes your ongoing tickets, priorities, external dependencies, and historical performance to provide intelligent recommendations. Get personalized suggestions on task sequencing, resource allocation, and optimal resolution strategies to maximize your efficiency.
                    </p>
                    <div class="flex flex-wrap gap-2">
                      <span class="px-3 py-1 bg-green-100 dark:bg-green-900/40 text-green-800 dark:text-green-300 rounded-full text-sm font-medium">Priority Guidance</span>
                      <span class="px-3 py-1 bg-green-100 dark:bg-green-900/40 text-green-800 dark:text-green-300 rounded-full text-sm font-medium">Dependency Tracking</span>
                      <span class="px-3 py-1 bg-green-100 dark:bg-green-900/40 text-green-800 dark:text-green-300 rounded-full text-sm font-medium">Personal Insights</span>
                    </div>
                  </div>
                  <div class="lg:w-1/2">
                    <div class="bg-white dark:bg-dark-surface rounded-xl p-6 shadow-lg border border-gray-200 dark:border-dark-border">
                      <img
                        src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=500&h=300&fit=crop&crop=center"
                        alt="Smart Work Assistant"
                        class="w-full h-48 object-cover rounded-lg mb-4"
                      />
                      <div class="text-center">
                        <div class="text-3xl font-bold text-green-600 dark:text-green-400"></div>
                        <div class="text-sm text-gray-600 dark:text-dark-text-secondary">Faster Task Completion</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Case 3: 自动化场景 -->
              <div class="bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-2xl p-8 border border-purple-100 dark:border-purple-800/30">
                <div class="flex flex-col lg:flex-row items-center gap-8">
                  <div class="lg:w-1/2">
                    <div class="flex items-center mb-4">
                      <div class="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center mr-4">
                        <n-icon size="24" color="white">
                          <FlashOutline />
                        </n-icon>
                      </div>
                      <h3 class="text-2xl font-semibold text-gray-900 dark:text-dark-text">Intelligent Automation</h3>
                    </div>
                    <p class="text-gray-700 dark:text-dark-text-secondary text-lg leading-relaxed mb-6">
                      Experience instant resolution with our automated response system. From server restarts to IP allocation, common requests are processed immediately upon submission, providing users with real-time solutions and reducing manual intervention.
                    </p>
                    <div class="flex flex-wrap gap-2">
                      <span class="px-3 py-1 bg-purple-100 dark:bg-purple-900/40 text-purple-800 dark:text-purple-300 rounded-full text-sm font-medium">Instant Response</span>
                      <span class="px-3 py-1 bg-purple-100 dark:bg-purple-900/40 text-purple-800 dark:text-purple-300 rounded-full text-sm font-medium">Auto Resolution</span>
                      <span class="px-3 py-1 bg-purple-100 dark:bg-purple-900/40 text-purple-800 dark:text-purple-300 rounded-full text-sm font-medium">24/7 Service</span>
                    </div>
                  </div>
                  <div class="lg:w-1/2">
                    <div class="bg-white dark:bg-dark-surface rounded-xl p-6 shadow-lg border border-gray-200 dark:border-dark-border">
                      <img
                        src="https://images.unsplash.com/photo-1518186285589-2f7649de83e0?w=500&h=300&fit=crop&crop=center"
                        alt="Intelligent Automation"
                        class="w-full h-48 object-cover rounded-lg mb-4"
                      />
                      <div class="text-center">
                        <div class="text-3xl font-bold text-purple-600 dark:text-purple-400"></div>
                        <div class="text-sm text-gray-600 dark:text-dark-text-secondary">Auto-Resolved Tickets</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 快速开始 -->
          <div class="bg-white dark:bg-dark-surface rounded-2xl p-8 shadow-sm border border-gray-100 dark:border-dark-border max-w-2xl mx-auto">
            <h2 class="text-2xl font-light text-gray-900 dark:text-dark-text mb-6">Get Started</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <n-button 
                size="large" 
                class="h-12 text-base font-medium"
                @click="navigateToCreated"
              >
                <template #icon>
                  <n-icon size="20">
                    <PersonOutline />
                  </n-icon>
                </template>
                View My Tickets
              </n-button>
              <n-button 
                type="primary" 
                size="large" 
                class="h-12 text-base font-medium bg-blue-600 dark:bg-dark-accent hover:bg-blue-700 dark:hover:bg-blue-500"
                @click="navigateToCreate"
              >
                <template #icon>
                  <n-icon size="20">
                    <AddOutline />
                  </n-icon>
                </template>
                Create New Ticket
              </n-button>
            </div>
          </div>
        </div>

        <!-- 底部信息 -->
        <div class="text-center">
          <div class="flex items-center justify-center space-x-6 text-sm text-gray-500 dark:text-dark-text-secondary mb-4">
            <div class="flex items-center">
              <n-icon size="16" class="mr-1">
                <ShieldCheckmarkOutline />
              </n-icon>
              Secure & Reliable
            </div>
            <div class="flex items-center">
              <n-icon size="16" class="mr-1">
                <FlashOutline />
              </n-icon>
              Efficient & Convenient
            </div>
            <div class="flex items-center">
              <n-icon size="16" class="mr-1">
                <CloudOutline />
              </n-icon>
              Cloud Sync
            </div>
          </div>
          <p class="text-xs text-gray-400 dark:text-dark-text-muted">
            © 2025 AI Ticket Management System.
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import { NIcon, NButton } from 'naive-ui';
import {
  DocumentsOutline,
  ClipboardOutline,
  PeopleOutline,
  BarChartOutline,
  PersonOutline,
  AddOutline,
  ShieldCheckmarkOutline,
  FlashOutline,
  CloudOutline,
  BulbOutline,
  TrendingUpOutline
} from '@vicons/ionicons5';
import { useUIStore } from '@/stores/ui';

const router = useRouter();
const uiStore = useUIStore();

const navigateToCreated = () => {
  // Show ticket list and navigate to Created by Me
  uiStore.ticketList.show();
  router.push('/tickets/created');
};

const navigateToCreate = () => {
  // Navigate to create ticket page
  router.push('/create');
};
</script>

<style scoped>
/* 优化后的样式 - 减少重复，使用 Tailwind 功能 */

/* Naive UI 按钮样式优化 */
:deep(.n-button) {
  border-radius: 0.75rem; /* rounded-xl */
  font-weight: 500; /* font-medium */
  transition: all 0.2s ease;
}

:deep(.n-button:hover) {
  transform: translateY(-1px);
}

/* 图标容器动画效果 */
.w-12.h-12 {
  transition: transform 0.2s ease;
}

/* 卡片悬停时的图标缩放效果 */
.hover\:shadow-md:hover .w-12.h-12 {
  transform: scale(1.05);
}
</style>