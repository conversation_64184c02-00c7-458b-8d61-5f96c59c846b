<template>
  <div class="h-full w-full overflow-y-auto bg-gray-50 dark:bg-dark-bg">
    <div class="p-6 max-w-7xl mx-auto">
      <!-- Header Section -->
      <div class="mb-6">
        <div class="flex items-center mb-4">
          <div class="w-10 h-10 bg-blue-100 dark:bg-dark-accent/20 rounded-lg flex items-center justify-center mr-4">
            <n-icon size="20" color="#3b82f6" class="dark:text-dark-accent">
              <AddCircleOutline />
            </n-icon>
          </div>
          <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-dark-text mb-1">Create New Ticket</h1>
          </div>
        </div>
      </div>

      <div>
        <FormWrapper ref="formRef" :model="formValue" :rules="rules">
          <!-- Basic Information Section -->
          <n-card class="mb-6 border border-gray-200">
            <template #header>
              <div class="flex items-center">
                <div class="w-7 h-7 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                  <n-icon size="16" color="#3b82f6">
                    <InformationCircleOutline />
                  </n-icon>
                </div>
                <span class="text-lg font-semibold text-gray-900 dark:text-dark-text">Basic Information</span>
              </div>
            </template>


            <n-grid :cols="24" :x-gap="24">
              <n-form-item-gi :span="12" v-for="(item, index) in list" :key="index" :label="item.label"
                :path="item.modelKey">
                <FormItemWrapper v-model="formValue[item.modelKey]" :form-item="item" />
              </n-form-item-gi>
            </n-grid>
          </n-card>

          <!-- Dynamic Service Fields Section -->
          <n-card v-if="selectedServicePackage" class="mb-6 border border-gray-200">
            <template #header>
              <div class="flex items-center">
                <div class="w-7 h-7 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                  <n-icon size="16" color="#10b981">
                    <SettingsOutline />
                  </n-icon>
                </div>
                <span class="text-lg font-semibold text-gray-900 dark:text-dark-text">Service Details</span>
              </div>
            </template>

            <!-- 多项服务详情表格 -->

            <ServiceItemsTable2 :list="fieldsList" v-slot="{ item, index }" @add="serviceItemAdd" @remove="serviceItemRemove" @copy="serviceItemCopy" @clear="serviceItemClear">
              <n-grid :cols="30" :x-gap="24">
                <n-form-item-gi :span="10" v-for="(sub, i) in item" :key="i" :label="sub.label"
                  :path="`${BASE_FIELDS}[${index}].${sub.modelKey}`">

                  <FormItemWrapper v-model="formValue[BASE_FIELDS][index][sub.modelKey]" :form-item="sub" />
                </n-form-item-gi>
              </n-grid>
            </ServiceItemsTable2>

          </n-card>

          <!-- Action Buttons -->
          <n-card class="border border-gray-200">
            <div class="flex justify-between items-center">
              <div class="text-sm text-gray-500 dark:text-dark-text-secondary">
                <n-icon size="16" class="mr-1">
                  <InformationCircleOutline />
                </n-icon>
                Please ensure all required information is filled correctly
              </div>
              <div class="flex space-x-3">
                <n-button size="small" @click="resetForm" class="px-4">
                  <template #icon>
                    <n-icon>
                      <RefreshOutline />
                    </n-icon>
                  </template>
                  Reset Form
                </n-button>

                <!-- AI建议按钮 -->
                <n-button size="small" @click="openAIPreassign"
                  class="px-4 bg-gradient-to-r from-indigo-600 to-indigo-700 hover:from-indigo-700 hover:to-indigo-800 dark:from-indigo-500 dark:to-indigo-600 dark:hover:from-indigo-600 dark:hover:to-indigo-700 text-white"
                  type="primary">
                  <template #icon>
                    <n-icon>
                      <SparklesOutline />
                    </n-icon>
                  </template>
                  AI Preassign
                </n-button>

                <n-button type="primary" size="small" @click="submitForm" :loading="loading"
                  class="px-4 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 dark:from-blue-500 dark:to-blue-600 dark:hover:from-blue-600 dark:hover:to-blue-700">
                  <template #icon>
                    <n-icon>
                      <CheckmarkCircleOutline />
                    </n-icon>
                  </template>
                  Create Ticket
                </n-button>
              </div>
            </div>
          </n-card>
        </FormWrapper>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, nextTick, onMounted, provide } from 'vue';
import { useRouter } from 'vue-router';
import {
  NInput,
  NSelect,
  NCascader,
  NButton,
  NCard,
  NIcon,
  useMessage
} from 'naive-ui';
import {
  AddCircleOutline,
  InformationCircleOutline,
  SettingsOutline,
  RefreshOutline,
  CheckmarkCircleOutline,
  SparklesOutline
} from '@vicons/ionicons5';
import { storeToRefs } from 'pinia';
import { useTicketStore } from '@/stores/ticket';
import { useUserStore } from '@/stores/user';
import { useUIStore } from '@/stores/ui';
import { useChatStore } from '@/stores/chat';
import { servicePackages, serializeFormData, createEmptyServiceItem } from '@/utils/servicePackages';
import type { Ticket, ServiceItem } from '@/types/ticket';
import { LOCATION_OPTIONS } from '@/types/ticket';
import ServiceItemsTable from '@/components/ServiceItemsTable.vue';
import ServiceItemsTable2 from '@/components/ServiceItemsTable2.vue';

import { NGrid, NFormItemGi } from 'naive-ui'
import FormWrapper from '@/components/FormWrapper.vue';
import FormItemWrapper from '@/components/FormItemWrapper.vue';
import { useFormValue } from '@/hooks/UseFormValue';
import { OPTIONS_KEYS } from "@/types/form";
import { FORM_LIST } from '@/utils/mock'
import { useOptionsStore } from '@/stores/options'
import { parseDescription, stringifyDescription, findParentId } from '@/utils/util'

const router = useRouter();
const message = useMessage();
const ticketStore = useTicketStore();
const userStore = useUserStore();
const uiStore = useUIStore();
const chatStore = useChatStore();

const { loading } = storeToRefs(ticketStore);

// 从UI store获取默认值
const defaults = uiStore.getTicketCreationDefaults();


const BASE_FIELDS = 'serviceItems'
const list = ref<any[]>([
  { 
      modelKey: 'summary', 
      modelValue: '', 
      modelType: 'string', 
      type: 'input', 
      label: 'Summary', 
      placeholder: 'please enter a value', 
      required: true,
      message: 'please enter a value'
    },
    { 
      modelKey: 'priority', 
      modelValue: '', 
      modelType: 'string', 
      type: 'select', 
      label: 'Priority', 
      placeholder: 'please enter a value', 
      optionKey: OPTIONS_KEYS['PRIORITY'] 
    },
    { 
      modelKey: 'components', 
      modelValue: '',
      modelType: 'string', 
      type: 'select', 
      label: 'Components', 
      placeholder: 'please enter a value', 
      optionKey: OPTIONS_KEYS['COMPONENTS'] 
    },
    { 
      modelKey: 'service_package', 
      modelValue: '', 
      modelType: 'string', 
      type: 'cascader', 
      label: 'Service Packages', 
      placeholder: 'please enter a value', 
      optionKey: OPTIONS_KEYS['SERVICE_PACKAGE'],
      props: {
        'on-update:value': (value: any, options: any, pathValues: any) => {
          fieldsList.value = []
          formValue['service_package'] = value
          if (options && options.fields && options.fields.length) {
            fields.value = options.fields
            serviceItemAdd()
          }
        }
      }
    },
])
const fieldsList = ref<any[]>([])
const fields = ref<any[]>([])
const formRef = ref()

const { formValue, rules, add, remove, reset } = useFormValue(list, { [BASE_FIELDS]: [] })


const optionsStore = useOptionsStore()
// const onSelectChange = (value: any, options: any, pathValues: any) => {

//   console.log(value, options, pathValues)
//   // if (modelKey === 'service_package') {
//   //   fieldsList.value = []
//   //   const options = optionsStore.getOptions(OPTIONS_KEYS['SERVICE_PACKAGE'])

//   //   const findFields = (list: any[]): any => {
//   //     for (let index = 0; index < list.length; index++) {
//   //       const item = list[index];
//   //       if (item.value === value) {
//   //         return item
//   //       }
//   //       if (item.children) {
//   //         const found = findFields(item.children)
//   //         if (found) return found
//   //       }
//   //     }

//   //     return null
//   //   }
//   //   const found = findFields(options)

//   //   if (found && found.fields && found.fields.length) {
//   //     fields.value = found.fields
//   //     serviceItemAdd()
     
//   //   }
//   // }
// }

const serviceItemAdd = () => {
  add(BASE_FIELDS, fields.value)
  fieldsList.value.push(fields.value)

  // setTimeout(() => {
  //   const list = parseDescription('||NO.||HW Name||Serial number||Installation Location||%0A|1|00|11|22|%0A', fields.value)

  //   formValue[BASE_FIELDS] = list
  // }, 2000);
}
const serviceItemRemove = (index: number) => {
  remove(BASE_FIELDS, index)
  fieldsList.value.splice(index, 1)
}
const serviceItemCopy = (index: number) => {
  const copyFields = fieldsList.value[index].map((item: any, i: number) => {
    item.modelValue = formValue[BASE_FIELDS][index][item.modelKey]
    return item
  });

  add(BASE_FIELDS, copyFields)
  fieldsList.value.push(copyFields)
}

const serviceItemClear = () => {
  fieldsList.value = []
  formValue[BASE_FIELDS] = []
}

const resetForm = () => {
  fieldsList.value = []
  reset()
};



const selectedServicePackage = computed(() => {
  return fieldsList.value.length || formValue['service_package'];
});


// Initialize service package related data when component is mounted
onMounted(() => {
  // 

});


// AI preassign feature
const openAIPreassign = async () => {

};


const submitForm = async () => {

  const result = await formRef.value.validate()
  if (!result) return

  try {
    let description = ''

    if (formValue[BASE_FIELDS]) {
      description = stringifyDescription(formValue[BASE_FIELDS], fields.value)
    }
    
    const options = optionsStore.getOptions(OPTIONS_KEYS['SERVICE_PACKAGE'])
    const parentId = findParentId(options, formValue.service_package)

    const ticketData: Partial<Ticket> = {
      summary: formValue.summary,
      // priority: formValue.priority,
      service_package: parentId || formValue.service_package,
      sub_service_package: parentId ? formValue.service_package : '',
      component: formValue.components, // Location information
      description,
    };

    console.log(ticketData)
    // return
    const newTicket = await ticketStore.createTicket(ticketData);

    message.success('Ticket created successfully!');

    uiStore.updateTicketCreationDefaults({
      priority: formValue.priority,
      component: formValue.component,
      service_package: formValue.service_package
    });

    resetForm()
    uiStore.ticketList.show();

    // Ensure ticket data is loaded, then navigate to "Created by Me" list
    await ticketStore.loadTickets();
    router.push('/tickets/created');
  } catch (error) {

    if (error && typeof error === 'object' && Array.isArray(error)) {
      // This is Naive UI validation error
      const validationErrors = error.flat();
      if (validationErrors.length > 0 && validationErrors[0].message) {
        message.warning('Please check error items in the form');
        return;
      }
    }

    message.error('Failed to create ticket, please try again');
  }
};
</script>

<style scoped>
/* Ensure text in input boxes is left-aligned */
:deep(.n-input__input-el) {
  text-align: left !important;
}

:deep(.n-input__textarea-el) {
  text-align: left !important;
}

/* Ensure text in select boxes is left-aligned */
:deep(.n-base-selection-input) {
  text-align: left !important;
}

:deep(.n-base-selection-placeholder) {
  text-align: left !important;
}
</style>