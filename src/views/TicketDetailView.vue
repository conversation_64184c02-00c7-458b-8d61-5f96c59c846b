<template>
  <div class="h-full w-full overflow-y-auto bg-gray-50 dark:bg-dark-bg">
    <div v-if="loading" class="flex items-center justify-center h-full">
      <n-spin size="large" />
    </div>

    <div v-else-if="currentTicket" class="p-6">
      <!-- Header Section -->
      <div class="mb-6">
        <n-card class="border-0 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-dark-surface dark:to-dark-bg">
          <div class="flex items-start justify-between">
            <div class="flex items-start space-x-4">
              <div class="w-14 h-14 bg-blue-100 dark:bg-blue-900/30 rounded-xl flex items-center justify-center">
                <n-icon 
                  :size="28" 
                  :color="getPriorityColor(currentTicket.priority)"
                  class="drop-shadow-sm"
                >
                  <component :is="getPriorityIcon(currentTicket.priority)" />
                </n-icon>
              </div>
              <div class="flex-1">
                <h1 class="text-2xl font-bold text-gray-900 dark:text-dark-text mb-2">
                  {{ currentTicket.summary }}
                </h1>
                <div class="flex items-center space-x-3 text-sm text-gray-600 dark:text-dark-text-secondary mb-3">
                  <span class="bg-white dark:bg-dark-surface px-2 py-1 rounded-full font-medium">{{ currentTicket.issues_key }}</span>
                  <span class="bg-white dark:bg-dark-surface px-2 py-1 rounded-full">Priority: {{ currentTicket.priority }}</span>
                  <span class="bg-white dark:bg-dark-surface px-2 py-1 rounded-full">Created: {{ formatDateTime(currentTicket.created) }}</span>
                </div>
              </div>
            </div>
            
            <div class="flex items-center space-x-3">
              <div class="text-right">
                <div class="text-sm text-gray-600 dark:text-dark-text-secondary mb-1">Current Status</div>
                <div class="flex items-center space-x-2">
                  <n-icon 
                    :size="20" 
                    :color="getStatusColor(currentTicket.status)"
                    class="drop-shadow-sm"
                  >
                    <component :is="getStatusIcon(currentTicket.status)" />
                  </n-icon>
                  <span class="text-lg font-bold" :style="{ color: getStatusColor(currentTicket.status) }">
                    {{ currentTicket.status }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </n-card>
      </div>

      <!-- Main Content Grid -->
      <div class="grid grid-cols-1 xl:grid-cols-3 gap-6">
        <!-- Left Column - Main Info -->
        <div class="xl:col-span-2 space-y-6">
          <!-- Basic Information -->
          <n-card class="border border-gray-200">
            <template #header>
              <div class="flex items-center">
                <div class="w-7 h-7 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                  <n-icon size="16" color="#3b82f6">
                    <InformationCircleOutline />
                  </n-icon>
                </div>
                <span class="text-lg font-semibold text-gray-900 dark:text-dark-text">Basic Information</span>
              </div>
            </template>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="space-y-3">
                <div class="bg-gray-50 dark:bg-dark-surface p-3 rounded-lg">
                  <div class="text-xs text-gray-600 dark:text-dark-text-secondary mb-1">Reporter</div>
                  <div class="font-semibold text-gray-900 dark:text-dark-text text-sm">{{ currentTicket.reporter_email }}</div>
                </div>
                <div class="bg-gray-50 dark:bg-dark-surface p-3 rounded-lg">
                  <div class="text-xs text-gray-600 dark:text-dark-text-secondary mb-1">Assignee</div>
                  <div class="font-semibold text-gray-900 dark:text-dark-text text-sm">{{ currentTicket.assignee_email }}</div>
                </div>
                <div class="bg-gray-50 dark:bg-dark-surface p-3 rounded-lg">
                  <div class="text-xs text-gray-600 dark:text-dark-text-secondary mb-1">Service Type</div>
                  <div class="font-semibold text-gray-900 dark:text-dark-text text-sm">{{ getServicePackageName(currentTicket.service_package) }}</div>
                </div>
              </div>
              
              <div class="space-y-3">
                <div v-if="currentTicket.component" class="bg-gray-50 dark:bg-dark-surface p-3 rounded-lg">
                  <div class="text-xs text-gray-600 dark:text-dark-text-secondary mb-1">Location</div>
                  <div class="font-semibold text-gray-900 dark:text-dark-text text-sm">{{ currentTicket.component }}</div>
                </div>
                <div class="bg-gray-50 dark:bg-dark-surface p-3 rounded-lg">
                  <div class="text-xs text-gray-600 dark:text-dark-text-secondary mb-1">Last Updated</div>
                  <div class="font-semibold text-gray-900 dark:text-dark-text text-sm">{{ formatDateTime(currentTicket.updated) }}</div>
                </div>
                <div v-if="currentTicket.resolved" class="bg-green-50 dark:bg-green-900/30 p-3 rounded-lg border border-green-200 dark:border-green-700">
                  <div class="text-xs text-green-600 dark:text-green-400 mb-1">Resolved Time</div>
                  <div class="font-semibold text-green-800 dark:text-green-300 text-sm">{{ formatDateTime(currentTicket.resolved) }}</div>
                </div>
              </div>
            </div>
          </n-card>

          <!-- Service Details -->
          <n-card class="border border-gray-200">
            <template #header>
              <div class="flex items-center">
                <div class="w-7 h-7 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                  <n-icon size="16" color="#10b981">
                    <SettingsOutline />
                  </n-icon>
                </div>
                <span class="text-lg font-semibold text-gray-900 dark:text-dark-text">Service Details</span>
              </div>
            </template>
            
            <!-- 原始内容展示 -->
            <div v-if="isRawContent" class="space-y-3">
              <div class="bg-gray-50 dark:bg-dark-surface p-4 rounded-lg border border-gray-100 dark:border-dark-border">
                <div class="text-xs text-gray-600 dark:text-dark-text-secondary mb-2">Raw Content</div>
                <div class="font-medium text-gray-900 dark:text-dark-text text-sm whitespace-pre-wrap">{{ (parsedDescription as any).rawContent }}</div>
              </div>
            </div>

            <!-- 多项服务数据展示 -->
            <div v-else-if="isMultipleService" class="space-y-4">
              <div
                v-for="(item, index) in (structuredData as ServiceItem[])"
                :key="item.id || index"
                class="bg-gray-50 dark:bg-dark-surface p-4 rounded-lg border border-gray-100 dark:border-dark-border"
              >
                <div class="text-sm font-medium text-gray-700 dark:text-dark-text mb-3">
                  {{ servicePackageInfo?.itemName || 'Item' }} {{ index + 1 }}
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div
                    v-for="(value, fieldName) in item.data"
                    :key="fieldName"
                    class="bg-white dark:bg-dark-bg p-3 rounded border dark:border-dark-border"
                  >
                    <div class="text-xs text-gray-600 dark:text-dark-text-secondary mb-1">{{ getFieldLabel(String(fieldName)) }}</div>
                    <div class="font-medium text-gray-900 dark:text-dark-text text-sm whitespace-pre-wrap">{{ value }}</div>
                  </div>
                </div>
              </div>
              <div v-if="(structuredData as ServiceItem[]).length === 0" class="text-center py-6 text-gray-500 dark:text-dark-text-secondary">
                <n-icon size="40" class="text-gray-300 dark:text-dark-text-muted mb-2">
                  <DocumentOutline />
                </n-icon>
                <div class="text-sm">No service items</div>
              </div>
            </div>

            <!-- 单项服务数据展示 -->
            <div v-else class="space-y-3">
              <div
                v-for="(value, key) in (structuredData as Record<string, any>)"
                :key="key"
                class="bg-gray-50 dark:bg-dark-surface p-3 rounded-lg border border-gray-100 dark:border-dark-border"
              >
                <div class="text-xs text-gray-600 dark:text-dark-text-secondary mb-1">{{ formatFieldName(key) }}</div>
                <div class="font-medium text-gray-900 dark:text-dark-text text-sm whitespace-pre-wrap">{{ value }}</div>
              </div>
              <div v-if="Object.keys(structuredData).length === 0" class="text-center py-6 text-gray-500 dark:text-dark-text-secondary">
                <n-icon size="40" class="text-gray-300 dark:text-dark-text-muted mb-2">
                  <DocumentOutline />
                </n-icon>
                <div class="text-sm">No detailed information</div>
              </div>
            </div>
          </n-card>
        </div>

        <!-- Right Column - Timeline & Work Log -->
        <div class="space-y-6">
          <!-- Work Log -->
          <n-card class="border border-gray-200">
            <template #header>
              <div class="flex items-center">
                <div class="w-7 h-7 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                  <n-icon size="16" color="#f59e0b">
                    <TimeOutline />
                  </n-icon>
                </div>
                <span class="text-lg font-semibold text-gray-900 dark:text-dark-text">Work Log</span>
              </div>
            </template>
            
            <div class="space-y-3">
              <!-- 总工时统计 -->
              <div v-if="totalWorkTime > 0" class="bg-blue-50 dark:bg-blue-900/30 p-3 rounded-lg border border-blue-200 dark:border-blue-700">
                <div class="text-xs text-blue-600 dark:text-blue-400 mb-1">Total Time Spent</div>
                <div class="text-xl font-bold text-blue-800 dark:text-blue-300">{{ formatWorkTime(totalWorkTime) }}</div>
              </div>

              <!-- 工作日志记录列表 -->
              <div v-if="currentTicket.worklogs && currentTicket.worklogs.length > 0" class="space-y-2">
                <div
                  v-for="(worklog, index) in sortedWorklogs"
                  :key="worklog.id"
                  class="bg-white dark:bg-dark-surface p-3 rounded-lg border border-gray-200 dark:border-dark-border shadow-sm"
                >
                  <!-- 第一行：作者、时间、工时 -->
                  <div class="flex items-center justify-between text-sm text-gray-600 mb-2">
                    <div class="flex items-center space-x-4">
                      <span class="font-medium text-gray-800 dark:text-dark-text">
                        {{ worklog.worklog_author || 'Unknown' }}
                      </span>
                      <span class="text-gray-500 dark:text-dark-text-secondary">
                        {{ worklog.worklog_time ? formatDateTime(worklog.worklog_time) : 'No time recorded' }}
                      </span>
                      <span v-if="worklog.worklog_timeSpent" class="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full text-xs font-medium">
                        {{ formatWorkTime(worklog.worklog_timeSpent) }}
                      </span>
                    </div>
                  </div>

                  <!-- 第二行：工作备注 -->
                  <div v-if="worklog.worklog_comment" class="text-sm text-gray-900 dark:text-dark-text leading-relaxed text-left">
                    {{ worklog.worklog_comment }}
                  </div>
                </div>
              </div>

              <!-- 空状态 -->
              <div v-if="!currentTicket.worklogs || currentTicket.worklogs.length === 0" class="text-center py-6 text-gray-500">
                <n-icon size="40" class="text-gray-300 mb-2">
                  <ClipboardOutline />
                </n-icon>
                <div class="text-sm">No work log</div>
              </div>
            </div>
          </n-card>

          <!-- Timeline -->
          <n-card class="border border-gray-200">
            <template #header>
              <div class="flex items-center">
                <div class="w-7 h-7 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                  <n-icon size="16" color="#8b5cf6">
                    <GitBranchOutline />
                  </n-icon>
                </div>
                <span class="text-lg font-semibold text-gray-900 dark:text-dark-text">Processing Timeline</span>
              </div>
            </template>
            
            <!-- 自定义时间线 -->
            <div class="relative">
              <!-- 工单创建 -->
              <div class="flex items-start pb-4">
                <div class="flex flex-col items-center mr-4">
                  <div class="w-3 h-3 bg-green-500 rounded-full border-2 border-white shadow-sm"></div>
                  <div class="w-0.5 h-8 bg-gray-300 mt-1"></div>
                </div>
                <div class="flex-1 min-w-0">
                  <div class="text-sm font-medium text-gray-900">{{ formatDateTime(currentTicket.created) }}</div>
                  <div class="text-xs text-gray-600 mt-1">Ticket Created</div>
                </div>
              </div>

              <!-- 处理中状态 -->
              <div v-if="currentTicket.status === 'In Progress'" class="flex items-start pb-4">
                <div class="flex flex-col items-center mr-4">
                  <div class="w-3 h-3 bg-blue-500 rounded-full border-2 border-white shadow-sm"></div>
                  <div class="w-0.5 h-8 bg-gray-300 mt-1"></div>
                </div>
                <div class="flex-1 min-w-0">
                  <div class="text-sm font-medium text-gray-900">{{ formatDateTime(currentTicket.updated) }}</div>
                  <div class="text-xs text-gray-600 mt-1">In Progress</div>
                </div>
              </div>

              <!-- 已解决状态 -->
              <div v-if="currentTicket.resolved" class="flex items-start pb-4">
                <div class="flex flex-col items-center mr-4">
                  <div class="w-3 h-3 bg-green-500 rounded-full border-2 border-white shadow-sm"></div>
                  <div v-if="currentTicket.status === 'Closed'" class="w-0.5 h-8 bg-gray-300 mt-1"></div>
                </div>
                <div class="flex-1 min-w-0">
                  <div class="text-sm font-medium text-gray-900">{{ formatDateTime(currentTicket.resolved) }}</div>
                  <div class="text-xs text-gray-600 mt-1">Resolved</div>
                </div>
              </div>

              <!-- 已关闭状态 -->
              <div v-if="currentTicket.status === 'Closed'" class="flex items-start">
                <div class="flex flex-col items-center mr-4">
                  <div class="w-3 h-3 bg-gray-500 rounded-full border-2 border-white shadow-sm"></div>
                </div>
                <div class="flex-1 min-w-0">
                  <div class="text-sm font-medium text-gray-900">{{ formatDateTime(currentTicket.updated) }}</div>
                  <div class="text-xs text-gray-600 mt-1">Closed</div>
                </div>
              </div>
            </div>
          </n-card>
        </div>
      </div>
    </div>

    <div v-else class="flex items-center justify-center h-full">
      <div class="text-center text-gray-500">
        <n-icon size="64" class="text-gray-300 mb-4">
          <DocumentOutline />
        </n-icon>
        <h3 class="text-lg font-medium text-gray-900 mb-2">Ticket Not Found</h3>
        <p class="text-sm">Please check if the ticket ID is correct</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue';
import { useRoute } from 'vue-router';
import { NCard, NSpin, NIcon } from 'naive-ui';
import { 
  DocumentOutline,
  CheckmarkCircleOutline,
  TimeOutline,
  PlayCircleOutline,
  StopCircleOutline,
  InformationCircleOutline,
  SettingsOutline,
  ClipboardOutline,
  GitBranchOutline,
  FlameOutline,
  WarningOutline,
  RemoveCircleOutline
} from '@vicons/ionicons5';
import { storeToRefs } from 'pinia';
import { useTicketStore } from '@/stores/ticket';
import type { ServiceItem } from '@/types/ticket';
import { parseDescription, servicePackages } from '@/utils/servicePackages';
import { format } from 'date-fns';

const route = useRoute();
const ticketStore = useTicketStore();

// Correctly use storeToRefs to get reactive references
const { currentTicket, loading } = storeToRefs(ticketStore);

const parsedDescription = computed(() => {
  if (!currentTicket.value) return {};
  return parseDescription(currentTicket.value.description, currentTicket.value.service_package);
});

// Get service package information
const servicePackageInfo = computed(() => {
  if (!currentTicket.value) return null;
  return servicePackages.find(sp => sp.id === currentTicket.value!.service_package);
});

// Check if the parsed description is raw content
const isRawContent = computed(() => {
  const parsed = parsedDescription.value;
  return parsed && typeof parsed === 'object' && 'rawContent' in parsed;
});

// Determine if it's a multi-item service
const isMultipleService = computed(() => {
  return servicePackageInfo.value?.allowMultiple || false;
});

// Get the actual parsed data (excluding raw content)
const structuredData = computed(() => {
  if (isRawContent.value) return servicePackageInfo.value?.allowMultiple ? [] : {};
  return parsedDescription.value;
});

// 获取字段标签
const getFieldLabel = (fieldName: string) => {
  if (!servicePackageInfo.value) return formatFieldName(fieldName);

  const field = servicePackageInfo.value.fields.find(f => f.name === fieldName);
  return field ? field.label : formatFieldName(fieldName);
};

// Priority icon mapping
const getPriorityIcon = (priority: string) => {
  const iconMap: Record<string, any> = {
    'Critical': FlameOutline,
    'High': WarningOutline,
    'Medium': InformationCircleOutline,
    'Low': RemoveCircleOutline,
    'Minor': RemoveCircleOutline
  };
  return iconMap[priority] || InformationCircleOutline;
};

// Priority color mapping
const getPriorityColor = (priority: string) => {
  const colorMap: Record<string, string> = {
    'Critical': '#ef4444',
    'High': '#f97316',
    'Medium': '#3b82f6',
    'Low': '#10b981',
    'Minor': '#9ca3af'
  };
  return colorMap[priority] || '#6b7280';
};

// Status icon mapping
const getStatusIcon = (status: string) => {
  const iconMap: Record<string, any> = {
    'Open': TimeOutline,
    'In Progress': PlayCircleOutline,
    'Resolved': CheckmarkCircleOutline,
    'Closed': StopCircleOutline
  };
  return iconMap[status] || TimeOutline;
};

// Status color mapping
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    'Open': '#3b82f6',
    'In Progress': '#f59e0b',
    'Resolved': '#10b981',
    'Closed': '#6b7280'
  };
  return colorMap[status] || '#6b7280';
};

const getServicePackageName = (id: string) => {
  const servicePackage = servicePackages.find(sp => sp.id === id);
  return servicePackage?.name || id;
};

const formatDateTime = (dateString: string) => {
  return format(new Date(dateString), 'yyyy-MM-dd HH:mm:ss');
};

const formatFieldName = (fieldName: string | number) => {
  // Ensure fieldName is a string
  const name = String(fieldName);
  return name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
};

// 格式化工作时间
const formatWorkTime = (hours: number) => {
  if (hours < 1) {
    return `${Math.round(hours * 60)} minutes`;
  } else if (hours === Math.floor(hours)) {
    return `${hours} hours`;
  } else {
    const wholeHours = Math.floor(hours);
    const minutes = Math.round((hours - wholeHours) * 60);
    return `${wholeHours} hours ${minutes} minutes`;
  }
};

// 计算总工时
const totalWorkTime = computed(() => {
  if (!currentTicket.value?.worklogs) return 0;
  return currentTicket.value.worklogs.reduce((total, worklog) =>
    total + (worklog.worklog_timeSpent || 0), 0);
});

// 按时间倒序排列的工作日志
const sortedWorklogs = computed(() => {
  if (!currentTicket.value?.worklogs) return [];
  return [...currentTicket.value.worklogs].sort((a, b) => {
    const timeA = new Date(a.worklog_time || 0).getTime();
    const timeB = new Date(b.worklog_time || 0).getTime();
    return timeB - timeA; // 倒序：最新的在前
  });
});

// Watch route changes to set current ticket from store
watch(() => route.params.id, async (id) => {
  if (id && typeof id === 'string') {
    // Set current ticket (now async)
    await ticketStore.setCurrentTicket(id);
  }
}, { immediate: true });
</script>