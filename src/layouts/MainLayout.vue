<template>
  <div class="fixed inset-0 flex transition-colors duration-300">
    <!-- Sidebar Navigation - Outlook style -->
    <div class="w-16 bg-blue-600 shadow-lg flex-shrink-0 relative z-30">
      <SidebarNav />
    </div>
    
    <!-- Ticket List Container with horizontal collapse -->
    <div 
      class="relative flex-shrink-0 z-20"
      @mouseenter="handleTicketListMouseEnter"
      @mouseleave="handleTicketListMouseLeave"
    >
      <!-- Ticket List Panel with thicker, darker border -->
      <div 
        class="transition-all duration-300 ease-in-out h-full overflow-hidden border-2 shadow-sm"
        :class="[
          theme === 'dark' ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-400'
        ]"
        :style="{ width: isTicketListExpanded ? '20rem' : '0px' }"
      >
        <div class="w-80 h-full">
          <TicketList />
        </div>
      </div>
      
      <!-- Ticket List Toggle Tab -->
      <div 
        class="absolute left-0 top-1/2 transform -translate-y-1/2 p-2 rounded-r-lg cursor-pointer hover:bg-gray-200 hover:text-gray-800 transition-all duration-200 shadow-lg z-10 flex items-center justify-center border border-gray-300"
        :class="[
          theme === 'dark' 
            ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' 
            : 'bg-white text-gray-600',
          { 'translate-x-0': !isTicketListExpanded, 'translate-x-80': isTicketListExpanded }
        ]"
        style="transition: transform 0.3s ease-in-out;"
        @click="toggleTicketList"
      >
        <n-icon 
          :size="18" 
          class="transition-transform duration-200"
        >
          <component :is="isTicketListExpanded ? ChevronBackOutline : ChevronForwardOutline" />
        </n-icon>
      </div>
      
      <!-- Hover trigger area - only show when not pinned and not expanded -->
      <div 
        v-if="!isTicketListExpanded && !isTicketListPinned"
        class="absolute left-0 top-0 w-4 h-full bg-transparent cursor-pointer"
      />
    </div>
    
    <!-- Main Content - Outlook style -->
    <div class="flex-1 min-w-0 border-r h-full relative z-10 transition-colors duration-300" :class="[
      theme === 'dark' ? 'border-gray-600' : 'border-gray-300'
    ]">
      <MainContent />
    </div>
    
    <!-- AI Assistant Panel Container -->
    <div 
      class="relative flex-shrink-0 z-20"
      @mouseenter="handleMouseEnter"
      @mouseleave="handleMouseLeave"
      ref="aiPanelContainer"
    >
      <!-- AI Panel -->
      <div 
        class="shadow-lg transition-all duration-300 ease-in-out h-full overflow-hidden border-l"
        :class="[
          theme === 'dark' ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-300'
        ]"
        :style="{ width: isAIPanelExpanded ? '22rem' : '0px' }"
      >
        <div class="w-88 h-full">
          <ChatbotPanel />
        </div>
      </div>
      
      <!-- AI Assistant Tab -->
      <div 
        class="absolute right-0 top-1/2 transform -translate-y-1/2 p-2 rounded-l-lg cursor-pointer hover:bg-gray-200 hover:text-gray-800 transition-all duration-200 shadow-lg z-10 flex items-center justify-center border border-gray-300"
        :class="[
          theme === 'dark' 
            ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' 
            : 'bg-white text-gray-600',
          { 'translate-x-0': !isAIPanelExpanded, '-translate-x-88': isAIPanelExpanded }
        ]"
        style="transition: transform 0.3s ease-in-out;"
        @click="toggleAI"
      >
        <n-icon 
          :size="18" 
          class="transition-transform duration-200"
        >
          <component :is="isAIPanelExpanded ? ChevronForwardOutline : ChevronBackOutline" />
        </n-icon>
      </div>
      
      <!-- Hover trigger area -->
      <div
        v-if="!isAIPanelExpanded && !isAIPanelPinned"
        class="absolute right-0 top-0 w-4 h-full bg-transparent cursor-pointer"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onUnmounted } from 'vue';
import { useRoute } from 'vue-router';
import { NIcon } from 'naive-ui';
import { ChevronForwardOutline, ChevronBackOutline } from '@vicons/ionicons5';
import { storeToRefs } from 'pinia';
import SidebarNav from '@/components/SidebarNav.vue';
import TicketList from '@/components/TicketList.vue';
import MainContent from '@/components/MainContent.vue';
import ChatbotPanel from '@/components/ChatbotPanel.vue';
import { useUIStore } from '@/stores/ui';
import { useChatStore } from '@/stores/chat';

const route = useRoute();

// AI Panel state
const aiPanelContainer = ref();

// Use UI Store to manage TicketList state, AI Panel state and theme
const uiStore = useUIStore();
const chatStore = useChatStore();
const { isTicketListVisible, isTicketListPinned, isAIPanelVisible, isAIPanelPinned, theme } = storeToRefs(uiStore);
const { messages, isStreaming } = storeToRefs(chatStore);

// TicketList expanded state based on visibility state in store
const isTicketListExpanded = computed(() => isTicketListVisible.value);

// AI Panel expanded state based on visibility state in store
const isAIPanelExpanded = computed(() => isAIPanelVisible.value);

// Panel control functions - directly from store
const toggleAI = () => uiStore.aiPanel.toggle();
const toggleTicketList = () => uiStore.ticketList.toggle();

// Event handlers - directly from store (no more composable coordination needed)
const handleMouseEnter = uiStore.aiPanel.handleMouseEnter;
const handleMouseLeave = uiStore.aiPanel.handleMouseLeave;
const handleTicketListMouseEnter = uiStore.ticketList.handleMouseEnter;
const handleTicketListMouseLeave = uiStore.ticketList.handleMouseLeave;

// Listen to chat message changes, automatically show AI panel when there are new messages
watch(messages, (newMessages, oldMessages) => {
  // If message count increased, there are new messages
  if (newMessages.length > (oldMessages?.length || 0)) {
    // Check if the latest message is a user message or system message (indicating a new message was just sent)
    const latestMessage = newMessages[newMessages.length - 1];
    if (latestMessage && (latestMessage.type === 'user' || latestMessage.type === 'system')) {
      // Automatically show AI panel
      if (!isAIPanelVisible.value) {
        uiStore.aiPanel.show();
        if (import.meta.env.DEV || import.meta.env.VITE_USE_MOCK_DATA === 'true') {
          console.log('Auto-showing AI panel due to new message:', latestMessage.type);
        }
      }
    }
  }
}, { deep: true });

// Listen to streaming status, ensure AI panel stays expanded during streaming
watch(isStreaming, (newValue) => {
  if (newValue && !isAIPanelVisible.value) {
    uiStore.aiPanel.show();
    if (import.meta.env.DEV) {
      console.log('Auto-showing AI panel due to streaming start');
    }
  }
});

// Listen to route changes, ensure TicketList stays expanded on ticket-related pages
watch(() => route.path, (newPath) => {
  // If navigating to ticket-related pages, ensure TicketList is expanded and pinned
  if (newPath.startsWith('/tickets/') || route.name === 'ticket-detail') {
    if (!isTicketListVisible.value) {
      uiStore.ticketList.show();
      if (import.meta.env.DEV) {
        console.log('Auto-showing TicketList for tickets page');
      }
    }
  }
}, { immediate: true });

// Listen to route name changes, handle ticket detail pages
watch(() => route.name, (newName) => {
  if (import.meta.env.DEV) {
    console.log('Route name changed to:', newName);
  }
  
  // If it's a ticket detail page, ensure TicketList is expanded
  if (newName === 'ticket-detail') {
    if (!isTicketListVisible.value) {
      uiStore.ticketList.show();
      if (import.meta.env.DEV) {
        console.log('Auto-showing TicketList for ticket detail page');
      }
    }
  }
}, { immediate: true });

// Listen to TicketList visibility changes
watch(() => isTicketListVisible.value, (newValue) => {
  if (import.meta.env.DEV) {
    console.log('TicketList visibility changed:', newValue, 'Pinned:', isTicketListPinned.value);
  }
});

// Clean up timers
onUnmounted(() => {
  uiStore.aiPanel.cleanup();
  uiStore.ticketList.cleanup();
});
</script>