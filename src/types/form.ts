

// form
export interface FormItemComponentProps {
    type: 'input' | 'select' | 'cascader',
    props: any,
    modelType: any,
    optionKey?: RecordOptionKey,
    placeholder?: string,
    required?: boolean,
    trigger?: string,
    message?: string,
}

export type FormItemValue = {
    modelKey: string,
    modelValue: any,
    label: string,
    path: string
} & FormItemComponentProps

// export type OmitFormItemValue = Omit<FormItemValue, 'formItemComponent'>
// export type PickFormItemValue = Pick<FormItemValue, 'modelKey' | 'modelValue' | 'formItemRule'>;

// options
export const OPTIONS_KEYS = {
  SERVICE_PACKAGE: "SERVICE_PACKAGE",
  PRIORITY: "PRIORITY",
  COMPONENTS: "COMPONENTS",
} as const;

export type ItemOptionArray = { label: string; value: number | string }[];

export type FetchFnType = () => Promise<ItemOptionArray>

export type ItemOptionValue = {
  loading: boolean;
  fetchFn: FetchFnType;
  options: ItemOptionArray
};

export type ItemOptionKey = (typeof OPTIONS_KEYS)[keyof typeof OPTIONS_KEYS];
export type RecordOptionKey = ItemOptionKey | Exclude<string, ItemOptionKey>;
