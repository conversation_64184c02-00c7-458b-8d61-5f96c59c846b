/**
 * UI状态模型: ChatMessage (前端内部)
 * 用于管理和渲染对话列表的状态结构
 */
export interface ChatMessage {
  id: string;          // 消息的唯一标识，用于Vue的key和更新定位
  content: string;     // 消息的文本内容
  type: 'user' | 'assistant' | 'system'; // 消息归属方，决定UI布局
  timestamp: Date;     // 消息创建时间
  status?: 'loading' | 'error' | 'done' | 'cancelled'; // 消息状态，用于UI反馈
  metadata?: {         // 元数据支持
    commandType?: string;
    requestId?: string;
    isStreaming?: boolean;  // 标识是否为流式命令
  };
}
