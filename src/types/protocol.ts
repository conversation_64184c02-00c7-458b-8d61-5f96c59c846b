/**
 * 通信协议: ClientMessage & ServerEvent (前后端交互)
 * 定义前端与后端之间交换信息的语言和规则
 */

import type { Ticket } from './ticket';

// 命令的具体载荷类型
export interface QueryDevicePayload {
  device_sn: string;
}

export interface VmResetPayload {
  vm_id: string;
  force_shutdown?: boolean;
}

export type TicketPreassignPayload = Ticket;

export interface PlanWorkPayload {
  // plan_work 命令不需要特定参数，使用空对象
}



// 基础消息接口
interface BaseMessage {
  messageId: string; // 客户端生成的唯一ID，用于端到端追踪
}

// 聊天消息
export interface ChatClientMessage extends BaseMessage {
  type: 'chat';
  payload: { content: string; };
  language: string;
}

// 命令消息 (泛型与条件类型)
export interface CommandClientMessage<T extends string> extends BaseMessage {
  type: 'command';
  command: T;
  language: string;
  payload: T extends 'query_device' ? QueryDevicePayload :
           T extends 'vm_reset' ? VmResetPayload :
           T extends 'ticket_preassign' ? TicketPreassignPayload :
           T extends 'plan_work' ? PlanWorkPayload :
           never; // 确保所有命令都有明确的payload类型
}

// 最终的客户端消息联合类型
export type ClientMessage =
  | ChatClientMessage
  | CommandClientMessage<'query_device'>
  | CommandClientMessage<'vm_reset'>
  | CommandClientMessage<'ticket_preassign'>
  | CommandClientMessage<'plan_work'>;

// 标准化服务端事件外壳
export interface ServerEvent<T = any> {
  event: 'token_chunk' | 'command_receipt' | 'error' | 'stream_end';
  data: T;
  requestId: string; // 回传客户端的messageId，用于追踪
  timestamp: number;
}

// 具体事件的数据类型
export interface TokenChunkData { 
  content: string; 
}

export interface CommandReceiptData {
  command: string;
  status: 'success' | 'failure';
  message: string;
  result?: any; // 可选的命令执行结果
}

export interface ErrorData { 
  code: number; 
  message: string;
  retryable?: boolean;  // 是否可重试
  context?: any;        // 错误上下文
}

export interface StreamEndData { 
  reason: 'completed' | 'stopped' | 'error'; 
}

// 类型化的服务端事件
export type TokenChunkEvent = ServerEvent<TokenChunkData>;
export type CommandReceiptEvent = ServerEvent<CommandReceiptData>;
export type ErrorEvent = ServerEvent<ErrorData>;
export type StreamEndEvent = ServerEvent<StreamEndData>;
