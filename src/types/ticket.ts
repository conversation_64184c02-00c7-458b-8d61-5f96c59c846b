export interface Worklog {
  id: number;
  worklog_author?: string;
  worklog_time?: string;
  worklog_timeSpent?: number;
  worklog_comment?: string;
  ticket_id: string; // 关联到 issues_id
}

export interface Ticket {
  issues_id: string;
  issues_key: string;
  ordering_business_line?: string;
  lab_location?: string;
  issue_type?: string;
  summary: string;
  assignee?: string;
  reporter?: string;
  priority: 'Low' | 'Medium' | 'High' | 'Critical' | 'Minor';
  status: 'Open' | 'In Progress' | 'Resolved' | 'Closed';
  resolution?: string;
  created: string;
  resolved?: string;
  updated: string;
  time_to_resolution?: string;
  service_package: string;
  sub_service_package?: string;
  feedback?: string;
  component?: string; // Location information - needs to be displayed in details and selected when creating
  watchers?: string;
  lsdsup_remaining_time?: string;
  yes_sla_missed?: number;
  description: string;
  assignee_email: string;
  reporter_email: string;
  worklogs?: Worklog[]; // 一对多关系：工作日志记录
  catalog?: string;
  vector_embedding?: any; // PostgreSQL vector type
  filtered_desc?: string;
  ai_decision_logs?: string;
  human_feedback_annotations?: string;
}

export interface ServicePackageField {
  name: string;
  label: string;
  type: 'input' | 'textarea' | 'select';
  required: boolean;
  options?: string[];
  width?: 'small' | 'medium' | 'large'; // Table column width configuration
  placeholder?: string; // Field placeholder
}

export interface ServicePackage {
  id: string;
  name: string;
  fields: ServicePackageField[];
  allowMultiple?: boolean; // Whether multiple items are supported
  maxItems?: number; // Maximum item count limit
  itemName?: string; // Item name (e.g. "Software", "Asset", etc.)
}

// Multi-item service data item
export interface ServiceItem {
  id: string; // Unique identifier
  data: Record<string, any>; // Field data
}



// Location options - two-level menu structure for component field
export const LOCATION_OPTIONS = [
  {
    label: 'Beijing Office',
    value: 'Beijing Office',
    children: [
      { label: '1F Reception', value: 'Beijing Office - 1F Reception' },
      { label: '2F Finance Dept', value: 'Beijing Office - 2F Finance Dept' },
      { label: '3F Development Dept', value: 'Beijing Office - 3F Development Dept' },
      { label: '4F Design Dept', value: 'Beijing Office - 4F Design Dept' },
      { label: '5F Conference Room', value: 'Beijing Office - 5F Conference Room' }
    ]
  },
  {
    label: 'Shanghai Office',
    value: 'Shanghai Office',
    children: [
      { label: '1F Lobby', value: 'Shanghai Office - 1F Lobby' },
      { label: '2F Sales Dept', value: 'Shanghai Office - 2F Sales Dept' },
      { label: '3F Marketing Dept', value: 'Shanghai Office - 3F Marketing Dept' }
    ]
  },
  {
    label: 'Shenzhen Office',
    value: 'Shenzhen Office',
    children: [
      { label: '1F Reception Area', value: 'Shenzhen Office - 1F Reception Area' },
      { label: '2F Technical Dept', value: 'Shenzhen Office - 2F Technical Dept' }
    ]
  },
  {
    label: 'Data Center',
    value: 'Data Center',
    children: [
      { label: 'Zone A Server Room', value: 'Data Center - Zone A Server Room' },
      { label: 'Zone B Server Room', value: 'Data Center - Zone B Server Room' },
      { label: 'Zone C Storage', value: 'Data Center - Zone C Storage' }
    ]
  },
  {
    label: 'Remote Work',
    value: 'Remote Work',
    children: [
      { label: 'Home', value: 'Remote Work - Home' }
    ]
  },
  {
    label: 'Customer Site',
    value: 'Customer Site',
    children: [
      { label: 'Temporary Deployment', value: 'Customer Site - Temporary Deployment' }
    ]
  }
];