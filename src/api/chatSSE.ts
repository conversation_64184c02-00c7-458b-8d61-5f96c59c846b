import type {
  ClientMessage,
  ServerEvent,
  TokenChunkData,
  CommandReceiptData,
  ErrorData,
  StreamEndData
} from '@/types/protocol';
import { sseRequest } from '@/utils/request';

/**
 * SSE事件处理器回调接口
 */
export interface SSEEventHandlers {
  onTokenChunk?: (data: TokenChunkData, event: ServerEvent<TokenChunkData>) => void;
  onCommandReceipt?: (data: CommandReceiptData, event: ServerEvent<CommandReceiptData>) => void;
  onError?: (data: ErrorData, event: ServerEvent<ErrorData>) => void;
  onStreamEnd?: (data: StreamEndData, event: ServerEvent<StreamEndData>) => void;
}

/**
 * SSE连接选项
 */
export interface SSEOptions {
  abortSignal?: AbortSignal;
  timeout?: number;
}

/**
 * 新的ChatSSE服务类，支持命令和聊天的统一处理
 */
export class ChatSSEService {
  /**
   * 发送消息并处理SSE响应
   */
  static async sendMessage(
    message: ClientMessage,
    handlers: SSEEventHandlers,
    options: SSEOptions = {}
  ): Promise<void> {
    const { abortSignal, timeout = 30000 } = options;

    try {
      // 检查是否已取消
      if (abortSignal?.aborted) {
        throw new Error('Request aborted');
      }

      // 在开发环境下或设置了使用 mock 数据时，使用模拟实现
      // if (import.meta.env.DEV || import.meta.env.VITE_USE_MOCK_DATA === 'true') {
      //   return this.simulateSSEResponse(message, handlers, options);
      // }

      // 生产环境下的真实SSE实现，使用统一的request工具
      const response = await sseRequest('/chat', {
        method: 'POST',
        body: message,
        signal: abortSignal,
        timeout
      });

      if (!response.body) {
        throw new Error('No response body');
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();

      try {
        while (true) {
          if (abortSignal?.aborted) {
            throw new Error('Request aborted');
          }

          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value, { stream: true });
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.trim() === '') continue;

            try {
              if (line.startsWith('event: ')) {
                // 处理具名事件
                const eventType = line.substring(7).trim();
                continue;
              }

              if (line.startsWith('data: ')) {
                const data = line.substring(6);
                const serverEvent: ServerEvent = JSON.parse(data);
                this.handleServerEvent(serverEvent, handlers);
              }
            } catch (parseError) {
              console.warn('Failed to parse SSE line:', line, parseError);
            }
          }
        }
      } finally {
        reader.releaseLock();
      }

    } catch (error) {
      if (error instanceof Error && handlers.onError) {
        handlers.onError({
          code: -1,
          message: error.message,
          retryable: !abortSignal?.aborted
        }, {
          event: 'error',
          data: { code: -1, message: error.message },
          requestId: message.messageId,
          timestamp: Date.now()
        });
      }
      throw error;
    }
  }

  /**
   * 处理服务端事件分发
   */
  private static handleServerEvent(event: ServerEvent, handlers: SSEEventHandlers): void {
    switch (event.event) {
      case 'token_chunk':
        handlers.onTokenChunk?.(event.data, event);
        break;
      case 'command_receipt':
        handlers.onCommandReceipt?.(event.data, event);
        break;
      case 'error':
        handlers.onError?.(event.data, event);
        break;
      case 'stream_end':
        handlers.onStreamEnd?.(event.data, event);
        break;
      default:
        console.warn('Unknown SSE event type:', event.event);
    }
  }

  /**
   * 创建取消控制器
   */
  static createAbortController(): AbortController {
    return new AbortController();
  }

  ////////////////////////////////////////////////////////////////////////////////////////////
  // 以下为开发环境下的模拟实现， 实际生产环境要跟后端进行对接


  /**
   * 模拟SSE响应（开发环境使用）
   */
  private static async simulateSSEResponse(
    message: ClientMessage,
    handlers: SSEEventHandlers,
    options: SSEOptions = {}
  ): Promise<void> {
    const { abortSignal } = options;

    try {
      // 检查是否已取消
      if (abortSignal?.aborted) {
        throw new Error('Request aborted');
      }

      // 模拟初始延迟
      await new Promise(resolve => setTimeout(resolve, 300 + Math.random() * 200));

      if (message.type === 'command') {
        // 处理命令消息
        await this.simulateCommandResponse(message, handlers, abortSignal);
      } else {
        // 处理聊天消息
        await this.simulateChatResponse(message, handlers, abortSignal);
      }

    } catch (error) {
      if (error instanceof Error && handlers.onError) {
        handlers.onError({
          code: -1,
          message: error.message,
          retryable: !abortSignal?.aborted
        }, {
          event: 'error',
          data: { code: -1, message: error.message },
          requestId: message.messageId,
          timestamp: Date.now()
        });
      }
      throw error;
    }
  }

  /**
   * 模拟命令响应
   */
  private static async simulateCommandResponse(
    message: ClientMessage & { type: 'command' },
    handlers: SSEEventHandlers,
    abortSignal?: AbortSignal
  ): Promise<void> {

    // 如果是流式命令，使用流式输出
    if ((message.command === 'query_device' || message.command === 'plan_work') && handlers.onTokenChunk) {
      if (message.command === 'query_device') {
        await this.simulateQueryDeviceStreaming(message, handlers, abortSignal);
      } else if (message.command === 'plan_work') {
        await this.simulatePlanWorkStreaming(message, handlers, abortSignal);
      }
    } else {
      // 其他命令使用原有的非流式处理
      await this.simulateNonStreamingCommand(message, handlers, abortSignal);
    }
  }

  /**
   * 模拟 query_device 命令的流式输出
   */
  private static async simulateQueryDeviceStreaming(
    message: ClientMessage & { type: 'command' },
    handlers: SSEEventHandlers,
    abortSignal?: AbortSignal
  ): Promise<void> {
    const logSteps = [
      '正在连接到设备管理系统...\n',
      '验证设备序列号...\n',
      '查询设备基本信息...\n',
      '获取设备状态信息...\n',
      '检索设备位置信息...\n',
      '查询完成！\n\n',
      '📋 设备信息详情：\n',
      `• 设备序列号: ${(message.payload as any).device_sn}\n`,
      '• 设备型号: Dell OptiPlex 7090\n',
      '• 运行状态: 🟢 Active\n',
      '• 所在位置: Beijing Office - 3F Development Dept\n',
      '• 最后更新: ' + new Date().toLocaleString() + '\n'
    ];

    for (const step of logSteps) {
      if (abortSignal?.aborted) {
        throw new Error('Request aborted');
      }

      // 发送流式文本块
      if (handlers.onTokenChunk) {
        handlers.onTokenChunk({ content: step }, {
          event: 'token_chunk',
          data: { content: step },
          requestId: message.messageId,
          timestamp: Date.now()
        });
      }

      // 模拟每个步骤的延迟
      await new Promise(resolve => setTimeout(resolve, 300 + Math.random() * 500));
    }

    // 发送命令执行结果（可选，用于状态更新）
    const commandResult = this.generateCommandResult(message.command, message.payload);
    if (handlers.onCommandReceipt) {
      handlers.onCommandReceipt(commandResult, {
        event: 'command_receipt',
        data: commandResult,
        requestId: message.messageId,
        timestamp: Date.now()
      });
    }

    // 发送流结束事件
    if (handlers.onStreamEnd) {
      handlers.onStreamEnd({ reason: 'completed' }, {
        event: 'stream_end',
        data: { reason: 'completed' },
        requestId: message.messageId,
        timestamp: Date.now()
      });
    }
  }

  /**
   * 模拟 plan_work 命令的流式输出
   */
  private static async simulatePlanWorkStreaming(
    message: ClientMessage & { type: 'command' },
    handlers: SSEEventHandlers,
    abortSignal?: AbortSignal
  ): Promise<void> {
    const planWorkContent = [
      '# Analysis of Open Tickets and Work Plan\n\n',
      '## Executive Summary\n',
      'You currently have **5 open tickets** (status: Open or In Progress), spanning diverse technical domains. This reflects the breadth of your responsibilities but also introduces challenges related to context switching.\n\n',

      '### Ticket Distribution (By Component):\n',
      '• **Database Servers**: 1 (TIC-214)\n',
      '• **Data Center - Zone A Server Room**: 1 (TIC-201)\n',
      '• **DevOps Tools**: 1 (TIC-206)\n',
      '• **Employee Hardware**: 1 (TIC-208)\n',
      '• **VPN Service**: 1 (TIC-212)\n\n',

      '### Oldest Ticket:\n',
      '**TIC-201** (Email server connection timeout), created on July 15, 2025. Though not Critical, it has been pending for over 2 days and requires attention.\n\n',

      '## Collaboration Analysis\n',
      'Your work involves collaboration across teams. Key collaborators include:\n\n',
      '• **<EMAIL>**: Involved in high-risk or critical tickets (e.g., TIC-201 firewall change, TIC-214 system alert). Collaboration is primarily approval, review, and task delegation—indicating a leadership or senior advisory role.\n\n',
      '• **<EMAIL>**: Engaged in peer-level technical troubleshooting (e.g., TIC-206 Git access issue).\n\n',
      '**Conclusion**: You are effectively leveraging team resources by escalating critical issues and collaborating with peers on technical investigations.\n\n',

      '## Immediate Priorities\n',
      'Based on priority, impact, and status, prioritize as follows:\n\n',
      '### 🔴 Highest Priority: TIC-214 (High CPU usage on database server)\n',
      'Only Critical ticket; impacts core production service. Immediate resolution is required after identifying the problematic query.\n\n',

      '### 🟡 Next Priority: TIC-212 (VPN connection outage)\n',
      'High-priority new ticket reported by a Director. Requires prompt diagnosis and solution to avoid business impact.\n\n',

      '### 🟢 Ongoing Follow-ups: TIC-201, TIC-206, TIC-208\n',
      'All at High priority but currently in waiting stages:\n',
      '• **TIC-201** (Email server): Awaiting user feedback\n',
      '• **TIC-206** (Git access): Awaiting network team action\n',
      '• **TIC-208** (Laptop screen flicker): Temporary replacement provided; awaiting hardware inspection\n\n',
      'These require proactive follow-up to prevent delays.\n\n',

      '## Optimization Plan\n',
      'To improve efficiency and reduce cognitive load:\n\n',

      '### 📅 Theme-Based Time Blocking\n',
      '**Issue**: Handling 5 different technical areas causes frequent context switching.\n\n',
      '**Recommendation**: Divide your day into focused work blocks:\n',
      '• **Morning (9:00–11:00)**: Infrastructure maintenance (TIC-214, TIC-201)\n',
      '• **Afternoon (14:00–15:00)**: User support (TIC-212, TIC-208)\n\n',
      '**Outcome**: Reduced switching overhead and increased depth of focus.\n\n',

      '### 🔄 Proactive Dependency Management\n',
      '**Issue**: TIC-206 progress depends on another team; passive waiting risks delay.\n\n',
      '**Recommendation**: Log all external requests clearly in worklogs (e.g., "Requested [task] from [team/person] at [time]"), and set internal reminders for follow-up (e.g., 4 hours later or next morning).\n\n',
      '**Outcome**: Improved cross-team coordination and reduced risk of stalled tickets due to communication gaps.\n\n',

      '---\n\n',
      '✅ **Work plan analysis completed successfully!**\n'
    ];

    for (const chunk of planWorkContent) {
      if (abortSignal?.aborted) {
        throw new Error('Request aborted');
      }

      // 发送流式文本块
      if (handlers.onTokenChunk) {
        handlers.onTokenChunk({ content: chunk }, {
          event: 'token_chunk',
          data: { content: chunk },
          requestId: message.messageId,
          timestamp: Date.now()
        });
      }

      // 模拟打字机效果的延迟
      await new Promise(resolve => setTimeout(resolve, 150 + Math.random() * 200));
    }

    // 发送命令执行结果（可选，用于状态更新）
    const commandResult = this.generateCommandResult(message.command, message.payload);
    if (handlers.onCommandReceipt) {
      handlers.onCommandReceipt(commandResult, {
        event: 'command_receipt',
        data: commandResult,
        requestId: message.messageId,
        timestamp: Date.now()
      });
    }

    // 发送流结束事件
    if (handlers.onStreamEnd) {
      handlers.onStreamEnd({ reason: 'completed' }, {
        event: 'stream_end',
        data: { reason: 'completed' },
        requestId: message.messageId,
        timestamp: Date.now()
      });
    }
  }

  /**
   * 模拟非流式命令响应
   */
  private static async simulateNonStreamingCommand(
    message: ClientMessage & { type: 'command' },
    handlers: SSEEventHandlers,
    abortSignal?: AbortSignal
  ): Promise<void> {
    // 模拟命令处理时间
    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));

    if (abortSignal?.aborted) {
      throw new Error('Request aborted');
    }

    // 发送命令执行结果
    const commandResult = this.generateCommandResult(message.command, message.payload);

    if (handlers.onCommandReceipt) {
      handlers.onCommandReceipt(commandResult, {
        event: 'command_receipt',
        data: commandResult,
        requestId: message.messageId,
        timestamp: Date.now()
      });
    }

    // 发送流结束事件
    if (handlers.onStreamEnd) {
      handlers.onStreamEnd({ reason: 'completed' }, {
        event: 'stream_end',
        data: { reason: 'completed' },
        requestId: message.messageId,
        timestamp: Date.now()
      });
    }
  }

  /**
   * 模拟聊天响应
   */
  private static async simulateChatResponse(
    message: ClientMessage & { type: 'chat' },
    handlers: SSEEventHandlers,
    abortSignal?: AbortSignal
  ): Promise<void> {
    const response = this.selectResponse(message.payload.content);
    const chars = Array.from(response);
    let fullText = '';

    for (let i = 0; i < chars.length; i += 2) {
      if (abortSignal?.aborted) {
        throw new Error('Request aborted');
      }

      const chunk = chars.slice(i, i + 2).join('');
      fullText += chunk;

      if (handlers.onTokenChunk) {
        handlers.onTokenChunk({ content: chunk }, {
          event: 'token_chunk',
          data: { content: chunk },
          requestId: message.messageId,
          timestamp: Date.now()
        });
      }

      // 动态延迟
      const delay = this.calculateDelay(chunk, 25);
      await new Promise(resolve => setTimeout(resolve, delay));
    }

    // 发送流结束事件
    if (handlers.onStreamEnd) {
      handlers.onStreamEnd({ reason: 'completed' }, {
        event: 'stream_end',
        data: { reason: 'completed' },
        requestId: message.messageId,
        timestamp: Date.now()
      });
    }
  }

  /**
   * 生成命令执行结果
   */
  private static generateCommandResult(command: string, payload: any): CommandReceiptData {
    switch (command) {
      case 'query_device':
        return {
          command,
          status: 'success',
          message: `Device ${payload.device_sn} information retrieved successfully`,
          result: {
            device_sn: payload.device_sn,
            model: 'Dell OptiPlex 7090',
            status: 'Active',
            location: 'Beijing Office - 3F Development Dept'
          }
        };

      case 'vm_reset':
        return {
          command,
          status: 'success',
          message: `Virtual machine ${payload.vm_id} has been reset successfully`,
          result: {
            vm_id: payload.vm_id,
            status: 'Running',
            reset_time: new Date().toISOString()
          }
        };

      case 'ticket_preassign':
        return {
          command,
          status: 'success',
          message: `Ticket ${payload.ticket_id} has been assigned to agent ${payload.agent_id}`,
          result: {
            ticket_id: payload.ticket_id,
            agent_id: payload.agent_id,
            assigned_time: new Date().toISOString()
          }
        };

      case 'plan_work':
        return {
          command,
          status: 'success',
          message: 'Work plan analysis completed successfully',
          result: {
            total_tickets: 5,
            critical_tickets: 1,
            high_priority_tickets: 4,
            analysis_time: new Date().toISOString()
          }
        };

      default:
        return {
          command,
          status: 'failure',
          message: `Unknown command: ${command}`
        };
    }
  }

  /**
   * 根据消息内容选择响应
   */
  private static selectResponse(message: string): string {
    // Mock响应数据
    const responses = [
      `I understand your inquiry. Based on the ticket management system context, I can provide the following assistance:

📋 Ticket Management Features:
• Create new tickets - Support multiple service types
• Ticket status tracking - Open, In Progress, Resolved, Closed
• Priority management - Critical, High, Medium, Low
• Assign and transfer tickets

🔧 Technical Support Services:
• Network troubleshooting and diagnosis
• Software installation and configuration
• Hardware maintenance and replacement
• System optimization and upgrades

📦 Asset Management:
• Device information query and updates
• Asset transfer and allocation
• Maintenance record management
• Warranty status tracking

📊 Data Analysis:
• Ticket processing efficiency statistics
• Problem type analysis
• Service quality assessment
• Trend prediction and recommendations

🎯 Best Practice Recommendations:
• Describe problem symptoms in detail
• Provide accurate device information
• Select appropriate priority
• Update ticket status promptly

What specific issues do you need me to help resolve?`,

    `Based on your query, I provide the following detailed information:

🖥️ Device Configuration Details:
Model: Dell OptiPlex 7090
Serial Number: PD31334
Asset Number: IT-2023-0156
Purchase Date: March 15, 2023
Warranty Period: Until March 14, 2026

💻 Hardware Specifications:
• CPU: Intel Core i7-11700 (8 cores 16 threads)
• Memory: 32GB DDR4-3200 (2x16GB)
• Storage: 1TB NVMe SSD + 2TB SATA HDD
• Graphics: NVIDIA GeForce RTX 3060 (12GB)
• Network: Intel I225-V Gigabit NIC + Wi-Fi 6E

📍 Usage Status:
Current Location: Beijing Office 3F Development Dept
User: Zhang San (<EMAIL>)
Department: Software Development
Status: In normal use

🔧 Maintenance Records:
Last Maintenance: November 20, 2024
Maintenance Content:
- Windows 11 system updates
- Driver updates
- Hardware health check
- Case cleaning and maintenance
- Performance optimization adjustments

Next Maintenance: February 20, 2025
Maintenance Responsible: Li Si (<EMAIL>)

📈 Performance Monitoring:
• CPU Usage: Average 15%, Peak 45%
• Memory Usage: 60% (19.2GB/32GB)
• Disk Usage: SSD 45%, HDD 30%
• Temperature Status: CPU 45°C, GPU 38°C
• Network Status: Normal, latency <5ms

Please feel free to ask if you need more technical details or have other questions!`,

    `Here's a comprehensive markdown example to test our rendering:

## Code Examples

### JavaScript Code Block
\`\`\`javascript
function createTicket(data) {
  const ticket = {
    id: generateId(),
    title: data.title,
    status: 'open',
    priority: data.priority || 'medium'
  };
  return ticket;
}
\`\`\`

### Inline Code
Use \`npm install marked\` to install the markdown parser.

## Links and References
- [Official Documentation](https://example.com/docs)
- [GitHub Repository](https://github.com/example/repo)

## Tables

| Feature | Status | Priority |
|---------|--------|----------|
| Markdown Support | ✅ Complete | High |
| Code Highlighting | 🔄 In Progress | Medium |
| Table Rendering | ✅ Complete | Low |

## Quotes
> "The best way to predict the future is to create it." - Peter Drucker

## Lists
1. **First item** with emphasis
2. *Second item* with italics
3. ~~Strikethrough text~~

### Nested Lists
- Main item
  - Sub item 1
  - Sub item 2
    - Deep nested item

---

**Bold text** and *italic text* work perfectly!`
  ];
    return responses[Math.floor(Math.random() * responses.length)];
  }

  /**
   * 计算延迟时间
   */
  private static calculateDelay(chunk: string, baseDelay: number): number {
    if (chunk.includes('\n')) return baseDelay * 2;
    if (chunk.match(/[，。！？；：,.!?;:]/)) return baseDelay * 2.5;
    if (chunk === ' ') return baseDelay * 1.5;

    return baseDelay + Math.random() * (baseDelay * 0.5);
  }
}