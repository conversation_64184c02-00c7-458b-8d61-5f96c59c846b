<script lang="ts" setup>
import { NForm, NGrid, NFormItemGi } from 'naive-ui';
import type { FormInst, FormProps } from 'naive-ui'
import { onUpdated, ref } from 'vue'

const formRef = ref<FormInst | null>(null)
const props = defineProps()

const validate = () => {
    return new Promise((resolve, reject) => {
        formRef.value?.validate((err) => {
            if (!err) {
                resolve(true)
            }else {
                reject(false)
            }
        })
    })
}

defineExpose({ validate })
</script>



<template>
    <n-form ref="formRef" v-bind="props">
        <slot />
    </n-form>
</template>