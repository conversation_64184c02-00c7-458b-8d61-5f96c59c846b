<template>
  <div class="service-items-table">
    <!-- 表格头部 -->
    <div class="flex items-center justify-between mb-4">
      <div class="flex items-center">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-dark-text">
          {{ servicePackage.itemName || 'Service Item' }} List
        </h3>
        <span class="ml-2 text-sm text-gray-500 dark:text-dark-text-secondary">
          ({{ items?.length || 0 }}/{{ servicePackage?.maxItems || '∞' }})
        </span>
      </div>
      <div class="flex space-x-2">
        <!-- 模板按钮 -->
        <n-dropdown
          v-if="availableTemplates?.length > 0"
          :options="templateOptions"
          trigger="click"
        >
          <n-button
            type="primary"
            size="small"
            class="bg-gradient-to-r from-slate-600 to-slate-700 hover:from-slate-700 hover:to-slate-800 dark:from-slate-500 dark:to-slate-600 dark:hover:from-slate-600 dark:hover:to-slate-700"
          >
            <template #icon>
              <n-icon>
                <LayersOutline />
              </n-icon>
            </template>
            Use Template
          </n-button>
        </n-dropdown>

        <!-- 添加按钮 -->
        <n-button
          type="primary"
          size="small"
          @click="addItem"
          :disabled="isMaxItemsReached"
          class="bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 dark:from-emerald-500 dark:to-emerald-600 dark:hover:from-emerald-600 dark:hover:to-emerald-700"
        >
          <template #icon>
            <n-icon>
              <AddCircleOutline />
            </n-icon>
          </template>
          Add {{ servicePackage.itemName || 'Item' }}
        </n-button>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="!items || items.length === 0" class="text-center py-8 bg-gray-50 dark:bg-dark-surface rounded-lg border-2 border-dashed border-gray-300 dark:border-dark-border">
      <n-icon size="48" color="#9ca3af" class="mb-4 dark:text-dark-text-muted">
        <DocumentTextOutline />
      </n-icon>
      <p class="text-gray-500 dark:text-dark-text-secondary mb-4">No {{ servicePackage.itemName || 'Service Items' }} yet</p>
      <n-button
        type="primary"
        size="small"
        @click="addItem"
        class="bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 dark:from-emerald-500 dark:to-emerald-600 dark:hover:from-emerald-600 dark:hover:to-emerald-700"
      >
        <template #icon>
          <n-icon>
            <AddCircleOutline />
          </n-icon>
        </template>
        Add First {{ servicePackage.itemName || 'Item' }}
      </n-button>
    </div>

    <!-- 表格内容 -->
    <div v-else-if="items && items.length > 0" class="space-y-3">
      <div
        v-for="(item, index) in items"
        :key="item.id"
        class="bg-white dark:bg-dark-surface border border-gray-200 dark:border-dark-border rounded-lg p-4 hover:shadow-md transition-shadow duration-200"
        :class="{
          'ring-2 ring-blue-500': draggedIndex === index,
          'ring-2 ring-green-400': dragOverIndex === index && draggedIndex !== index
        }"
        draggable="true"
        @dragstart="startDrag(index)"
        @dragover="onDragOver($event, index)"
        @drop="onDrop($event, index)"
        @dragend="onDragEnd"
      >
        <!-- 项目头部 -->
        <div class="flex items-center justify-between mb-3">
          <div class="flex items-center">
            <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-3">
              <span class="text-xs font-medium text-blue-600">{{ index + 1 }}</span>
            </div>
            <span class="text-sm font-medium text-gray-700 dark:text-dark-text">
              {{ servicePackage.itemName || 'Item' }} {{ index + 1 }}
            </span>
          </div>
          <div class="flex items-center space-x-2">
            <!-- 拖拽手柄 -->
            <n-button
              text
              size="small"
              @mousedown="startDrag(index)"
              class="cursor-move text-gray-400 dark:text-dark-text-muted hover:text-gray-600 dark:hover:text-dark-text"
            >
              <template #icon>
                <n-icon>
                  <ReorderThreeOutline />
                </n-icon>
              </template>
            </n-button>
            <!-- 复制按钮 -->
            <n-button
              text
              size="small"
              @click="duplicateItem(index)"
              :disabled="isMaxItemsReached"
              class="text-blue-500 hover:text-blue-700"
              title="Copy this item"
            >
              <template #icon>
                <n-icon>
                  <CopyOutline />
                </n-icon>
              </template>
            </n-button>
            <!-- 删除按钮 -->
            <n-button
              text
              size="small"
              @click="removeItem(index)"
              class="text-red-500 hover:text-red-700"
              title="Delete this item"
            >
              <template #icon>
                <n-icon>
                  <TrashOutline />
                </n-icon>
              </template>
            </n-button>
          </div>
        </div>

        <!-- 字段表单 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div
            v-for="field in servicePackage.fields"
            :key="field.name"
            :class="getFieldColSpan(field.width)"
            class="space-y-2"
          >
            <div class="text-sm font-medium text-gray-700 dark:text-dark-text">
              {{ field.label }}
              <span v-if="field.required" class="text-red-500 ml-1">*</span>
            </div>
            <n-input
              v-if="field.type === 'input'"
              v-model:value="item.data[field.name]"
              :placeholder="field.placeholder || `Please enter ${field.label}`"
              size="small"
              @input="onFieldChange"
            />
            <n-input
              v-else-if="field.type === 'textarea'"
              v-model:value="item.data[field.name]"
              :placeholder="field.placeholder || `Please enter ${field.label}`"
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 4 }"
              size="small"
              @input="onFieldChange"
            />
            <n-select
              v-else-if="field.type === 'select'"
              v-model:value="item.data[field.name]"
              :options="field.options?.map(opt => ({ label: opt, value: opt })) || []"
              :placeholder="field.placeholder || `Please select ${field.label}`"
              size="small"
              @update:value="onFieldChange"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 批量操作和提示 -->
    <div v-if="items && items.length > 0" class="mt-4 pt-4 border-t border-gray-200">
      <div class="flex items-center justify-between">
        <div class="text-sm text-gray-500 dark:text-dark-text-secondary">
          <div>Total {{ items.length }} {{ servicePackage?.itemName || 'Items' }}</div>
          <div class="text-xs text-gray-400 dark:text-dark-text-muted mt-1">
            💡 Tip: Drag to reorder, Ctrl+Enter to add quickly
          </div>
        </div>
        <div class="flex space-x-2">
          <n-button
            v-if="items.length > 1"
            size="small"
            @click="clearAllItems"
            type="error"
            ghost
          >
            <template #icon>
              <n-icon>
                <TrashOutline />
              </n-icon>
            </template>
            Clear All
          </n-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import {
  NButton,
  NIcon,
  NInput,
  NSelect,
  NDropdown,
  useDialog,
  useMessage
} from 'naive-ui';
import {
  AddCircleOutline,
  TrashOutline,
  DocumentTextOutline,
  ReorderThreeOutline,
  LayersOutline,
  CopyOutline
} from '@vicons/ionicons5';
import type { ServicePackage, ServiceItem } from '@/types/ticket';
import { createEmptyServiceItem, generateServiceItemId } from '@/utils/servicePackages';
import { getTemplatesForServicePackage, applyTemplate as applyServiceTemplate } from '@/utils/serviceTemplates';

interface Props {
  servicePackage: ServicePackage;
  modelValue: ServiceItem[];
}

interface Emits {
  (e: 'update:modelValue', value: ServiceItem[]): void;
  (e: 'change'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const dialog = useDialog();
const message = useMessage();
const draggedIndex = ref<number | null>(null);
const dragOverIndex = ref<number | null>(null);

const items = computed({
  get: () => props.modelValue || [],
  set: (value) => emit('update:modelValue', value)
});

const isMaxItemsReached = computed(() => {
  return props.servicePackage?.maxItems ? (items.value?.length || 0) >= props.servicePackage.maxItems : false;
});

// 模板相关
const availableTemplates = computed(() => {
  return props.servicePackage?.id ? getTemplatesForServicePackage(props.servicePackage.id) : [];
});

const templateOptions = computed(() => {
  return availableTemplates.value?.map(template => ({
    label: template.name,
    key: template.id,
    props: {
      onClick: () => applyTemplate(template.id)
    }
  })) || [];
});

// 添加项目
const addItem = () => {
  if (isMaxItemsReached.value) {
    message.warning(`Maximum ${props.servicePackage?.maxItems} ${props.servicePackage?.itemName || 'items'} allowed`);
    return;
  }

  if (!props.servicePackage?.id) {
    message.error('Please select service type first');
    return;
  }

  const newItem = createEmptyServiceItem(props.servicePackage.id);
  items.value = [...(items.value || []), newItem];
  emit('change');
};

// 删除项目
const removeItem = (index: number) => {
  if (items.value.length === 1) {
    dialog.warning({
      title: 'Confirm Delete',
      content: `After deletion, there will be no ${props.servicePackage.itemName || 'items'}. Are you sure you want to delete?`,
      positiveText: 'Confirm Delete',
      negativeText: 'Cancel',
      onPositiveClick: () => {
        items.value = items.value.filter((_, i) => i !== index);
        emit('change');
      }
    });
  } else {
    items.value = items.value.filter((_, i) => i !== index);
    emit('change');
  }
};

// 复制项目
const duplicateItem = (index: number) => {
  if (isMaxItemsReached.value) {
    message.warning(`Maximum ${props.servicePackage.maxItems} ${props.servicePackage.itemName || 'items'} allowed`);
    return;
  }

  const originalItem = items.value[index];
  const duplicatedItem: ServiceItem = {
    id: generateServiceItemId(),
    data: { ...originalItem.data }
  };

  // 在原项目后面插入复制的项目
  const newItems = [...items.value];
  newItems.splice(index + 1, 0, duplicatedItem);
  items.value = newItems;
  emit('change');

  message.success('Item copied');
};

// 清空所有项目
const clearAllItems = () => {
  dialog.warning({
    title: 'Confirm Clear',
    content: `Are you sure you want to clear all ${props.servicePackage?.itemName || 'items'}? This action cannot be undone.`,
    positiveText: 'Confirm Clear',
    negativeText: 'Cancel',
    onPositiveClick: () => {
      items.value = [];
      emit('change');
    }
  });
};

// 字段变化处理
const onFieldChange = () => {
  emit('change');
};

// 应用模板
const applyTemplate = (templateId: string) => {
  const template = availableTemplates.value.find(t => t.id === templateId);
  if (!template) return;

  if (items.value.length > 0) {
    dialog.warning({
      title: 'Apply Template',
      content: `Applying template will replace all current ${props.servicePackage.itemName || 'items'}. Are you sure you want to continue?`,
      positiveText: 'Confirm Apply',
      negativeText: 'Cancel',
      onPositiveClick: () => {
        items.value = applyServiceTemplate(template);
        emit('change');
        message.success(`Template applied: ${template.name}`);
      }
    });
  } else {
    items.value = applyServiceTemplate(template);
    emit('change');
    message.success(`Template applied: ${template.name}`);
  }
};

// 拖拽相关
const startDrag = (index: number) => {
  draggedIndex.value = index;
};

const onDragOver = (event: DragEvent, index: number) => {
  event.preventDefault();
  dragOverIndex.value = index;
};

const onDrop = (event: DragEvent, dropIndex: number) => {
  event.preventDefault();

  if (draggedIndex.value !== null && draggedIndex.value !== dropIndex) {
    const newItems = [...items.value];
    const draggedItem = newItems[draggedIndex.value];

    // 移除拖拽的项目
    newItems.splice(draggedIndex.value, 1);

    // 插入到新位置
    const insertIndex = draggedIndex.value < dropIndex ? dropIndex - 1 : dropIndex;
    newItems.splice(insertIndex, 0, draggedItem);

    items.value = newItems;
    emit('change');

    message.success('Item order adjusted');
  }

  draggedIndex.value = null;
  dragOverIndex.value = null;
};

const onDragEnd = () => {
  draggedIndex.value = null;
  dragOverIndex.value = null;
};

// 获取字段列跨度
const getFieldColSpan = (width?: string) => {
  switch (width) {
    case 'small':
      return 'md:col-span-1';
    case 'large':
      return 'md:col-span-2 lg:col-span-3';
    case 'medium':
    default:
      return 'md:col-span-1 lg:col-span-1';
  }
};

// 监听服务包变化，重置数据
watch(() => props.servicePackage?.id, () => {
  if (!items.value || items.value.length === 0) {
    addItem();
  }
}, { immediate: true });

// 键盘快捷键支持
const handleKeydown = (event: KeyboardEvent) => {
  // Ctrl/Cmd + Enter 添加新项目
  if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
    event.preventDefault();
    if (!isMaxItemsReached.value) {
      addItem();
    }
  }
};

// 组件挂载时添加键盘监听
onMounted(() => {
  document.addEventListener('keydown', handleKeydown);
});

// 组件卸载时移除键盘监听
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown);
});
</script>

<style scoped>
.service-items-table {
  width: 100%;
}
</style>
