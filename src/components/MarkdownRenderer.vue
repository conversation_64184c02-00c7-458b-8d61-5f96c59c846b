<template>
  <div 
    v-html="renderedContent" 
    class="markdown-content text-left"
    :class="themeClass"
  ></div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { marked } from 'marked';

interface Props {
  content: string;
  messageType?: 'user' | 'assistant' | 'system';
}

const props = withDefaults(defineProps<Props>(), {
  messageType: 'assistant'
});

// 配置marked选项
const markedOptions = {
  breaks: true, // 支持换行
  gfm: true,    // GitHub Flavored Markdown
  sanitize: false, // 允许HTML（在受控环境中）
  silent: true  // 静默错误处理
};

// 设置marked配置
marked.setOptions(markedOptions);

const renderedContent = computed(() => {
  try {
    return marked.parse(props.content);
  } catch (error) {
    // 如果markdown解析失败，回退到纯文本
    console.warn('Markdown parsing failed, falling back to plain text:', error);
    return props.content.replace(/\n/g, '<br>');
  }
});

// 根据消息类型应用不同的主题样式
const themeClass = computed(() => {
  return {
    'markdown-assistant': props.messageType === 'assistant',
    'markdown-system': props.messageType === 'system',
    'markdown-user': props.messageType === 'user'
  };
});
</script>

<style scoped>
/* 基础markdown样式 */
.markdown-content {
  line-height: 1.6;
  word-wrap: break-word;
}

/* 代码块样式 */
.markdown-content :deep(code) {
  background: rgba(0, 0, 0, 0.05);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875em;
}

.markdown-content :deep(pre) {
  background: rgba(0, 0, 0, 0.05);
  padding: 0.75rem;
  border-radius: 0.375rem;
  overflow-x: auto;
  margin: 0.5rem 0;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.markdown-content :deep(pre code) {
  background: transparent;
  padding: 0;
  border-radius: 0;
}

/* 链接样式 */
.markdown-content :deep(a) {
  color: #3b82f6;
  text-decoration: underline;
  word-break: break-all;
}

.markdown-content :deep(a:hover) {
  color: #1d4ed8;
}

/* 列表样式 */
.markdown-content :deep(ul),
.markdown-content :deep(ol) {
  margin: 0.5rem 0;
  padding-left: 1.5rem;
}

.markdown-content :deep(li) {
  margin: 0.25rem 0;
}

/* 标题样式 */
.markdown-content :deep(h1),
.markdown-content :deep(h2),
.markdown-content :deep(h3),
.markdown-content :deep(h4),
.markdown-content :deep(h5),
.markdown-content :deep(h6) {
  margin: 0.75rem 0 0.5rem 0;
  font-weight: 600;
  line-height: 1.4;
}

.markdown-content :deep(h1) { font-size: 1.25em; }
.markdown-content :deep(h2) { font-size: 1.125em; }
.markdown-content :deep(h3) { font-size: 1.0625em; }
.markdown-content :deep(h4) { font-size: 1em; }
.markdown-content :deep(h5) { font-size: 0.9375em; }
.markdown-content :deep(h6) { font-size: 0.875em; }

/* 段落样式 */
.markdown-content :deep(p) {
  margin: 0.5rem 0;
}

.markdown-content :deep(p:first-child) {
  margin-top: 0;
}

.markdown-content :deep(p:last-child) {
  margin-bottom: 0;
}

/* 引用样式 */
.markdown-content :deep(blockquote) {
  border-left: 4px solid #e5e7eb;
  padding-left: 1rem;
  margin: 0.5rem 0;
  color: #6b7280;
  font-style: italic;
}

/* 表格样式 */
.markdown-content :deep(table) {
  border-collapse: collapse;
  width: 100%;
  margin: 0.5rem 0;
  font-size: 0.875em;
}

.markdown-content :deep(th),
.markdown-content :deep(td) {
  border: 1px solid #e5e7eb;
  padding: 0.375rem 0.5rem;
  text-align: left;
}

.markdown-content :deep(th) {
  background: rgba(0, 0, 0, 0.05);
  font-weight: 600;
}

/* 水平分割线 */
.markdown-content :deep(hr) {
  border: none;
  border-top: 1px solid #e5e7eb;
  margin: 1rem 0;
}

/* 强调样式 */
.markdown-content :deep(strong) {
  font-weight: 600;
}

.markdown-content :deep(em) {
  font-style: italic;
}

/* 删除线 */
.markdown-content :deep(del) {
  text-decoration: line-through;
}

/* 针对不同消息类型的特定样式 */

/* Assistant消息样式（默认） */
.markdown-assistant :deep(code) {
  background: #f1f5f9;
  color: #334155;
}

.markdown-assistant :deep(pre) {
  background: #f8fafc;
  border-color: #e2e8f0;
}

/* System消息样式（黄色主题） */
.markdown-system :deep(code) {
  background: #fef3c7;
  color: #92400e;
}

.markdown-system :deep(pre) {
  background: #fffbeb;
  border-color: #fde68a;
}

.markdown-system :deep(blockquote) {
  border-left-color: #f59e0b;
  color: #92400e;
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .markdown-content :deep(code) {
    background: rgba(255, 255, 255, 0.1);
    color: #e2e8f0;
  }

  .markdown-content :deep(pre) {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .markdown-content :deep(a) {
    color: #60a5fa;
  }

  .markdown-content :deep(a:hover) {
    color: #93c5fd;
  }

  .markdown-content :deep(blockquote) {
    border-left-color: #4b5563;
    color: #9ca3af;
  }

  .markdown-content :deep(th),
  .markdown-content :deep(td) {
    border-color: #374151;
  }

  .markdown-content :deep(th) {
    background: rgba(255, 255, 255, 0.05);
  }

  .markdown-content :deep(hr) {
    border-top-color: #374151;
  }

  /* 暗色主题下的system消息 */
  .markdown-system :deep(code) {
    background: rgba(251, 191, 36, 0.2);
    color: #fbbf24;
  }

  .markdown-system :deep(pre) {
    background: rgba(251, 191, 36, 0.1);
    border-color: rgba(251, 191, 36, 0.3);
  }
}
</style>
