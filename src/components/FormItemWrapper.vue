<script setup lang="ts">
import { NFormItem, NInput, NSelect, NCascader, NFormItemGi } from 'naive-ui';
import { computed, onMounted, onUpdated, watch, useAttrs, h } from 'vue';
import type { FormItemComponentProps } from '@/types/form';
import { useOptionsStore } from '@/stores/options'

interface Props {
  formItem: FormItemComponentProps
}

const model = defineModel<any>()
const props = defineProps<Props>()
const components = {
    input: NInput,
    select: NSelect,
    cascader: NCascader
} as const

const optionsStore = useOptionsStore()

const bindProps = computed(() => {

  let baseProps = {
    ...(props.formItem.props || {}),
    placeholder: props.formItem.placeholder
  }

  if (props.formItem.optionKey) {
    baseProps = {
      ...baseProps,
      options: optionsStore.getOptions(props.formItem.optionKey),
      loading: optionsStore.getLoading(props.formItem.optionKey),
    }
  }

  return baseProps
})

watch(
  () => props.formItem.optionKey,
  key => {
    if (key) optionsStore.loadOptions(key)
  },
  { immediate: true }
)

const renderLabel = (option: any) => {
  return h(
    'div',
    {
      style: {
        whiteSpace: 'normal', // 允许换行
        wordBreak: 'break-word', // 长词也能断行
        lineHeight: '1.5'
      }
    },
    option.label
  )
}
</script>


<template>
    <!-- <n-form-item-gi :span="12" :label="props.formItem.label" :path="props.formItem.path"> -->
    <component :is="components[props.formItem.type]" v-model:value="model" v-bind="bindProps" :render-label="renderLabel"></component>
    <!-- </n-form-item-gi> -->
</template>


<style>
.n-cascader-menu .n-cascader-option  {
  height: auto;
  min-height: var(--n-option-height);
}
</style>