<template>
  <div class="h-full flex flex-col bg-white dark:bg-dark-surface">
    <!-- Header - Outlook style -->
    <div class="p-4 bg-blue-600 dark:bg-dark-accent text-white flex items-center justify-between">
      <div class="flex items-center">
        <n-icon size="20" class="text-white">
          <ChatbubbleEllipsesOutline />
        </n-icon>
        <span class="ml-2 font-medium">AI Assistant</span>
      </div>
    </div>

    <!-- Messages -->
    <div class="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50 dark:bg-dark-bg" ref="messagesContainer">
      <div v-if="messages.length === 0" class="text-center text-gray-500 dark:text-dark-text-secondary mt-8">
        <n-icon size="48" class="text-gray-300 dark:text-dark-text-muted mb-4">
          <ChatbubbleEllipsesOutline />
        </n-icon>
        <p class="text-lg font-medium text-gray-700 dark:text-dark-text">Start conversation with AI Assistant</p>
        <p class="text-sm mt-2 text-gray-500 dark:text-dark-text-secondary">I can help you with ticket-related questions</p>
      </div>

      <div
        v-for="message in messages"
        :key="message.id"
        :class="[
          'flex',
          message.type === 'user' ? 'justify-end' : 'justify-start'
        ]"
      >
        <div
          :class="[
            'max-w-xs px-4 py-3 rounded-lg break-words shadow-sm',
            message.type === 'user'
              ? 'bg-blue-600 dark:bg-dark-accent text-white'
              : message.type === 'system'
              ? 'bg-yellow-50 dark:bg-dark-warning text-yellow-800 dark:text-yellow-200 border border-yellow-200 dark:border-dark-warning'
              : 'bg-white dark:bg-dark-surface text-gray-900 dark:text-dark-text border border-gray-200 dark:border-dark-border'
          ]"
        >
          <!-- 命令类型标识 -->
          <div v-if="message.metadata?.commandType" class="flex items-center mb-2">
            <n-icon size="14" class="text-blue-500 mr-2">
              <DesktopOutline v-if="message.metadata.commandType === 'query_device'" />
              <ServerOutline v-else-if="message.metadata.commandType === 'vm_reset'" />
              <CalendarOutline v-else-if="message.metadata.commandType === 'plan_work'" />
              <DocumentTextOutline v-else />
            </n-icon>
            <span class="text-xs text-blue-600 font-medium">
              {{ getCommandDisplayName(message.metadata.commandType) }}
            </span>
          </div>

          <!-- 消息内容渲染 -->
          <div v-if="message.type === 'user'" class="whitespace-pre-wrap text-left">
            {{ message.content }}
          </div>
          <MarkdownRenderer
            v-else
            :content="message.content"
            :message-type="message.type"
            class="text-left"
          />
          <div class="text-xs opacity-70 mt-2 text-left flex items-center">
            <span>{{ formatMessageTime(message.timestamp) }}</span>

            <!-- 状态指示器 - 放在时间后面 -->
            <div v-if="message.status === 'loading'" class="flex items-center ml-2">
              <div class="w-3 h-3 border-2 border-current border-t-transparent rounded-full animate-spin mr-1"></div>
              <span class="text-xs">Processing...</span>
            </div>
            <div v-else-if="message.status === 'error'" class="flex items-center ml-2">
              <span class="text-xs mr-1">❌</span>
              <span class="text-xs text-red-600">Error</span>
            </div>
            <div v-else-if="message.status === 'cancelled'" class="flex items-center ml-2">
              <span class="text-xs mr-1">⏹️</span>
              <span class="text-xs text-gray-600">Cancelled</span>
            </div>
            <div v-else-if="message.status === 'done'" class="flex items-center ml-2">
              <span class="text-xs mr-1">✅</span>
              <span class="text-xs text-green-600">Success</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Typing indicator -->
      <!-- <div v-if="isStreaming" class="flex justify-start">
        <div class="bg-white dark:bg-dark-surface text-gray-900 dark:text-dark-text px-4 py-3 rounded-lg border border-gray-200 dark:border-dark-border shadow-sm">
          <div class="flex items-center">
            <div class="flex space-x-1">
              <div class="w-2 h-2 bg-blue-500 dark:bg-dark-accent rounded-full animate-bounce"></div>
              <div class="w-2 h-2 bg-blue-500 dark:bg-dark-accent rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
              <div class="w-2 h-2 bg-blue-500 dark:bg-dark-accent rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
            </div>
            <span class="ml-2 text-sm text-gray-600 dark:text-dark-text-secondary">Analyzing...</span>
          </div>
        </div>
      </div> -->
    </div>

    <!-- Command Buttons -->
    <div class="px-4 py-2 bg-gray-50 dark:bg-dark-bg border-t border-gray-200 dark:border-dark-border">
      <div class="flex gap-2">
        <n-tooltip trigger="hover">
          <template #trigger>
            <n-button
              size="small"
              type="tertiary"
              @click="sendDeviceQuery"
              :disabled="isStreaming"
            >
              <template #icon>
                <n-icon><DesktopOutline /></n-icon>
              </template>
            </n-button>
          </template>
          Query Device
        </n-tooltip>

        <n-tooltip trigger="hover">
          <template #trigger>
            <n-button
              size="small"
              type="tertiary"
              @click="sendVmReset"
              :disabled="isStreaming"
            >
              <template #icon>
                <n-icon><RefreshOutline /></n-icon>
              </template>
            </n-button>
          </template>
          Reset VM
        </n-tooltip>

        <n-tooltip trigger="hover">
          <template #trigger>
            <n-button
              size="small"
              type="tertiary"
              @click="sendTicketPreassign"
              :disabled="isStreaming"
            >
              <template #icon>
                <n-icon><DocumentTextOutline /></n-icon>
              </template>
            </n-button>
          </template>
          Ticket Preassign
        </n-tooltip>

        <n-tooltip trigger="hover">
          <template #trigger>
            <n-button
              size="small"
              type="tertiary"
              @click="sendPlanWork"
              :disabled="isStreaming"
            >
              <template #icon>
                <n-icon><CalendarOutline /></n-icon>
              </template>
            </n-button>
          </template>
          Plan Work
        </n-tooltip>
      </div>
    </div>

    <!-- Input - Outlook style -->
    <div class="p-4 bg-white dark:bg-dark-surface border-t border-gray-200 dark:border-dark-border">
      <div class="flex items-center space-x-3">
        <div class="flex-1 min-w-0">
          <n-input
            v-model:value="inputMessage"
            placeholder="Enter your question..."
            @keydown.enter="handleEnterKey"
            :disabled="isLoading || isStreaming"
            ref="inputRef"
            class="chat-input"
          >
            <template #prefix>
              <n-button
                text
                @click="clearMessages"
                :disabled="messages.length === 0 || isLoading || isStreaming"
                class="text-gray-400 dark:text-dark-text-muted hover:text-red-500 dark:hover:text-dark-error mr-1"
                size="small"
              >
                <template #icon>
                  <n-icon size="14">
                    <TrashOutline />
                  </n-icon>
                </template>
              </n-button>
            </template>
          </n-input>
        </div>
        <n-button
          type="primary"
          @click="handleSendButtonClick"
          :disabled="(!inputMessage.trim() || isLoading) && !isStreaming"
          :loading="isLoading"
          class="bg-blue-600 hover:bg-blue-700 flex-shrink-0 w-8 h-8 p-0 flex items-center justify-center"
        >
          <n-icon size="14">
            <StopOutline v-if="isStreaming" />
            <SendSharp v-else />
          </n-icon>
        </n-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, watch, onMounted, onUnmounted } from 'vue';
import { NButton, NIcon, NInput, NTooltip } from 'naive-ui';
import {
  ChatbubbleEllipsesOutline,
  TrashOutline,
  SendSharp,
  StopOutline,
  DesktopOutline,
  RefreshOutline,
  ServerOutline,
  DocumentTextOutline,
  CalendarOutline
} from '@vicons/ionicons5';
import { storeToRefs } from 'pinia';
import { useChatStore } from '@/stores/chat';
import { useTicketStore } from '@/stores/ticket';
import { useUserStore } from '@/stores/user';
import { format } from 'date-fns';
import MarkdownRenderer from '@/components/MarkdownRenderer.vue';

const chatStore = useChatStore();
const ticketStore = useTicketStore();
const userStore = useUserStore();
// 使用 storeToRefs 来获取响应式引用
const { messages, isLoading, isStreaming } = storeToRefs(chatStore);

const inputMessage = ref('');
const inputRef = ref();
const messagesContainer = ref();

// 自动滚动到底部的函数
const scrollToBottom = () => {
  if (messagesContainer.value) {
    nextTick(() => {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
    });
  }
};

// 监听消息变化，自动滚动到底部
let scrollTimer: NodeJS.Timeout | null = null;

const scheduleScroll = () => {
  if (scrollTimer) {
    clearTimeout(scrollTimer);
  }
  scrollTimer = setTimeout(() => {
    scrollToBottom();
  }, 10);
};

// 监听消息数组的变化
watch(messages, () => {
  scheduleScroll();
}, { deep: true, flush: 'post' });

// 监听流式输出状态
watch(isStreaming, (newValue) => {
  if (newValue) {
    // 开始流式输出时，设置定时器持续滚动
    const interval = setInterval(() => {
      scrollToBottom();
    }, 50);
    
    // 当流式输出结束时清除定时器
    const stopWatching = watch(isStreaming, (streamingValue) => {
      if (!streamingValue) {
        clearInterval(interval);
        stopWatching();
        // 最后滚动一次确保完全到底部
        setTimeout(() => {
          scrollToBottom();
        }, 100);
      }
    });
  }
});

const handleEnterKey = (event: KeyboardEvent) => {
  // 如果按下的是 Enter 键且没有按住 Shift 键
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault(); // 阻止默认的换行行为
    // 只有在没有加载和流式输出时才允许发送消息
    if (!isLoading.value && !isStreaming.value) {
      sendMessage();
    }
  }
  // 如果按下 Shift + Enter，允许换行（默认行为）
};

const handleSendButtonClick = () => {
  if (isStreaming.value) {
    stopStreaming();
  } else {
    sendMessage();
  }
};

const sendMessage = async () => {
  if (!inputMessage.value.trim() || isLoading.value || isStreaming.value) return;

  const message = inputMessage.value.trim();
  inputMessage.value = '';

  // 滚动到底部
  scrollToBottom();

  if (import.meta.env.DEV) {
    console.log('Sending message:', message);
  }

  await chatStore.sendMessage(message);

  // 发送完成后再次滚动到底部
  setTimeout(() => {
    scrollToBottom();
  }, 100);
};

const clearMessages = () => {
  if (import.meta.env.DEV) {
    console.log('Clearing messages...');
  }
  chatStore.clearMessages();
};

const stopStreaming = () => {
  if (import.meta.env.DEV) {
    console.log('Stopping streaming...');
  }
  chatStore.stopStreaming();
};

const formatMessageTime = (timestamp: Date) => {
  return format(timestamp, 'HH:mm');
};

// 获取命令显示名称
const getCommandDisplayName = (commandType: string) => {
  const commandNames: Record<string, string> = {
    'query_device': 'Device Query',
    'vm_reset': 'VM Reset',
    'ticket_preassign': 'Ticket Assignment',
    'plan_work': 'Work Plan Analysis'
  };
  return commandNames[commandType] || commandType;
};

// 命令按钮处理方法
const sendDeviceQuery = () => {
  chatStore.sendCommand('query_device', { device_sn: 'PD31334' });
};

const sendVmReset = () => {
  chatStore.sendCommand('vm_reset', { vm_id: 'vm-123', force_shutdown: false });
};

const sendTicketPreassign = () => {
  // 获取一个示例ticket实例 - 优先使用已分配的ticket，如果没有则使用创建的ticket
  const sampleTicket = ticketStore.assignedTickets[0] || ticketStore.createdTickets[0];

  if (sampleTicket) {
    chatStore.sendCommand('ticket_preassign', sampleTicket);
  } else {
    // 如果没有ticket数据，使用一个模拟的ticket实例
    const mockTicket = {
      issues_id: 'TK-2024-001',
      issues_key: 'TIC-001',
      summary: 'Sample ticket for preassignment demo',
      status: 'Open',
      priority: 'Medium',
      assignee_email: '<EMAIL>',
      reporter_email: '<EMAIL>'
    };
    chatStore.sendCommand('ticket_preassign', mockTicket);
  }
};

const sendPlanWork = () => {
  const userEmail = userStore.getCurrentUserEmail();
  chatStore.sendCommand('plan_work', { userEmail: userEmail});
};

// 组件挂载时滚动到底部
onMounted(() => {
  scrollToBottom();
});

// 组件卸载时清理定时器
onUnmounted(() => {
  if (scrollTimer) {
    clearTimeout(scrollTimer);
  }
});
</script>

<style scoped>
/* @keyframes bounce {
  0%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-bounce {
  animation: bounce 1s infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
} */

/* 确保输入框内的文字左对齐 */
:deep(.chat-input .n-input__input-el) {
  text-align: left !important;
}

:deep(.chat-input .n-input__textarea-el) {
  text-align: left !important;
}

:deep(.chat-input .n-input__placeholder) {
  text-align: left !important;
}

/* 确保所有消息内容都左对齐 */
.text-left {
  text-align: left !important;
}
</style>