<template>
  <n-modal
    v-model:show="showModal"
    preset="dialog"
    title="System Configuration"
    :mask-closable="false"
    style="width: 500px"
  >
    <template #header>
      <div class="flex items-center space-x-2">
        <n-icon size="20">
          <SettingsOutline />
        </n-icon>
        <span>System Configuration</span>
      </div>
    </template>

    <div class="space-y-6">
      <!-- Ticket History Days -->
      <div class="space-y-2">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Ticket retention period (days)
        </label>
        <n-input-number
          v-model:value="formData.ticket_history_days"
          :min="1"
          :max="365"
          :step="1"
          placeholder="Enter number of days"
          class="w-full"
        />
        <p class="text-xs text-gray-500 dark:text-gray-400">
          Number of days to fetch tickets from Jira Server (1-365 days)
        </p>
      </div>

      <!-- Ticket Refresh Interval -->
      <div class="space-y-2">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Ticket refresh interval (minutes)
        </label>
        <n-input-number
          v-model:value="formData.ticket_refresh_interval"
          :min="1"
          :max="60"
          :step="1"
          placeholder="Enter refresh interval"
          class="w-full"
        />
        <p class="text-xs text-gray-500 dark:text-gray-400">
          Interval for incremental ticket fetching from backend (1-60 minutes)
        </p>
      </div>

      <!-- Auto Refresh Enabled -->
      <div class="space-y-2">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Auto Refresh
        </label>
        <n-switch
          v-model:value="formData.autoRefreshEnabled"
        />
        <p class="text-xs text-gray-500 dark:text-gray-400">
          Enable or disable automatic ticket refresh
        </p>
      </div>

      <!-- Ticket Refresh From (Read-only) -->
      <div class="space-y-2">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Last Refresh Time
        </label>
        <n-input
          :value="formData.ticket_refresh_from ? new Date(formData.ticket_refresh_from).toLocaleString() : 'Not set'"
          readonly
          disabled
          placeholder="Last refresh time"
          class="w-full"
        />
      </div>
    </div>

    <template #action>
      <div class="flex justify-end space-x-3">
        <n-button @click="handleCancel">
          Cancel
        </n-button>
        <n-button
          type="primary"
          @click="handleSave"
          :loading="saving"
        >
          Save
        </n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import {
  NModal,
  NButton,
  NInputNumber,
  NInput,
  NIcon,
  NSwitch,
  useMessage
} from 'naive-ui';
import { SettingsOutline } from '@vicons/ionicons5';
import { useUIStore } from '@/stores/ui';

// Props
interface Props {
  show: boolean;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:show': [value: boolean];
}>();

// Store and message
const uiStore = useUIStore();
const message = useMessage();

// Modal state
const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
});

const saving = ref(false);

// Form data
const formData = reactive({
  ticket_history_days: 60,
  ticket_refresh_interval: 2,
  ticket_refresh_from: null as string | null,
  autoRefreshEnabled: true
});

// Initialize form data when modal opens
watch(() => props.show, (show) => {
  if (show) {
    const config = uiStore.getTicketConfig();
    formData.ticket_history_days = config.ticket_history_days;
    formData.ticket_refresh_interval = config.ticket_refresh_interval;
    formData.ticket_refresh_from = config.ticket_refresh_from;
    formData.autoRefreshEnabled = config.autoRefreshEnabled;
  }
});

// Handle cancel
const handleCancel = () => {
  emit('update:show', false);
};

// Handle save
const handleSave = async () => {
  try {
    saving.value = true;

    // Validate form data
    if (formData.ticket_history_days < 1 || formData.ticket_history_days > 365) {
      message.error('Ticket history days must be between 1-365 days');
      return;
    }

    if (formData.ticket_refresh_interval < 1 || formData.ticket_refresh_interval > 60) {
      message.error('Ticket refresh interval must be between 1-60 minutes');
      return;
    }

    // Update UI store
    uiStore.updateTicketConfig({
      ticket_history_days: formData.ticket_history_days,
      ticket_refresh_interval: formData.ticket_refresh_interval,
      autoRefreshEnabled: formData.autoRefreshEnabled
      // Note: ticket_refresh_from is read-only and not updated here
    });

    message.success('Configuration saved successfully');
    emit('update:show', false);
  } catch (error) {
    console.error('Error saving config:', error);
    message.error('Failed to save configuration');
  } finally {
    saving.value = false;
  }
};
</script>
