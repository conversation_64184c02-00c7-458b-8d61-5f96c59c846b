<template>
  <div class="h-full flex flex-col bg-white dark:bg-dark-surface">
    <!-- Header - 使用灰色系背景，比主体背景稍深 -->
    <div class="p-4 bg-gray-200 dark:bg-dark-bg border-b border-gray-300 dark:border-dark-border">
      <div class="flex items-center justify-between">
        <h2 class="text-lg font-semibold text-gray-800 dark:text-dark-text text-left">
          {{ currentTitle }}
        </h2>
        <n-button
          text
          size="small"
          @click="toggleSearchBox"
          class="text-gray-600 dark:text-dark-text-muted hover:text-gray-800 dark:hover:text-dark-text"
        >
          <template #icon>
            <n-icon size="16">
              <SearchOutline />
            </n-icon>
          </template>
        </n-button>
      </div>

      <!-- Divider - 始终显示 -->
      <div class="mt-3 border-t border-gray-300 dark:border-dark-border"></div>

      <!-- Search Box -->
      <div v-if="showSearchBox" class="mt-3">
        <n-input
          ref="searchInputRef"
          v-model:value="searchQuery"
          placeholder="Search ticket ID or title..."
          clearable
          size="small"
        >
          <template #prefix>
            <n-icon size="16" class="text-gray-400 dark:text-dark-text-muted">
              <SearchOutline />
            </n-icon>
          </template>
        </n-input>
      </div>

      <!-- Status Filter -->
      <div class="mt-2">
        <div class="flex items-center justify-between">
          <div class="flex gap-1.5">
            <n-tooltip
              v-for="status in availableStatuses"
              :key="status.value"
              placement="top"
              :show-arrow="false"
            >
              <template #trigger>
                <div
                  @click="toggleStatusFilter(status.value)"
                  data-testid="status-filter-option"
                  :title="status.label"
                  :class="[
                    'flex items-center justify-center w-6 h-5 rounded cursor-pointer transition-all duration-200 border',
                    selectedStatuses.includes(status.value)
                      ? 'border-blue-200 bg-blue-100 dark:border-blue-400 dark:bg-blue-900 shadow-sm'
                      : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
                  ]"
                >
                  <n-icon
                    :size="12"
                    :color="selectedStatuses.includes(status.value) ? '#1d4ed8' : '#9ca3af'"
                  >
                    <component :is="status.icon" />
                  </n-icon>
                </div>
              </template>
              <template #default>
                <span class="text-xs">{{ status.label }}</span>
              </template>
            </n-tooltip>
          </div>

          <div class="text-xs text-gray-500 dark:text-gray-400">
            {{ filteredTickets.length }} tickets
          </div>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex-1 flex items-center justify-center bg-gray-50 dark:bg-dark-bg">
      <n-spin size="medium" />
    </div>

    <!-- Empty State -->
    <div v-else-if="!filteredTickets || filteredTickets.length === 0" class="flex-1 flex items-center justify-center bg-gray-50 dark:bg-dark-bg">
      <div class="text-center text-gray-500 dark:text-dark-text-secondary">
        <n-icon size="48" class="text-gray-300 dark:text-dark-text-muted mb-4">
          <DocumentsOutline />
        </n-icon>
        <p class="text-lg font-medium text-gray-700 dark:text-dark-text">
          {{ searchQuery ? 'No matching tickets found' : 'No tickets' }}
        </p>
        <p class="text-sm text-gray-500 dark:text-dark-text-secondary mt-1">
          {{ searchQuery ? 'Please try other search keywords' : 'No tickets in current category' }}
        </p>
      </div>
    </div>

    <!-- Ticket List - Outlook style -->
    <div v-else class="flex-1 overflow-y-auto bg-gray-50 dark:bg-dark-bg">
      <div
        v-for="ticket in filteredTickets"
        :key="ticket.issues_id"
        @click="selectTicket(ticket)"
        :class="[
          'p-4 border-b border-gray-100 dark:border-dark-border cursor-pointer transition-all duration-150',
          'hover:bg-blue-50 dark:hover:bg-dark-surface-hover',
          selectedTicketId === ticket.issues_id
            ? 'bg-blue-100 dark:bg-dark-surface border-l-4 border-l-blue-500 dark:border-l-dark-accent'
            : 'hover:border-l-4 hover:border-l-blue-300 dark:hover:border-l-dark-accent'
        ]"
      >
        <!-- First Row: Priority icon + issues_key and updated time + status icon + menu -->
        <div class="flex items-center justify-between mb-2">
          <div class="flex items-center">
            <!-- Priority Icon -->
            <n-icon 
              :size="16" 
              :color="getPriorityColor(ticket.priority)"
              class="drop-shadow-sm mr-2"
            >
              <component :is="getPriorityIcon(ticket.priority)" />
            </n-icon>
            <p class="text-xs text-blue-600 font-medium">
              {{ ticket.issues_key }}
            </p>
          </div>
          
          <div class="flex items-center">
            <span class="text-xs text-gray-500 mr-2">
              {{ formatDate(ticket.updated) }}
            </span>
            <!-- Status Icon -->
            <n-icon 
              :size="16" 
              :color="getStatusColor(ticket.status)"
              class="drop-shadow-sm mr-2"
            >
              <component :is="getStatusIcon(ticket.status)" />
            </n-icon>
            <!-- Menu Icon -->
            <n-dropdown
              :options="getMenuOptions(ticket)"
              @select="handleMenuSelect"
              trigger="click"
              placement="bottom-end"
              @click.stop
            >
              <n-button
                text
                size="small"
                class="text-gray-400 dark:text-dark-text-muted hover:text-gray-600 dark:hover:text-dark-text p-1"
                @click.stop
              >
                <template #icon>
                  <n-icon size="16">
                    <EllipsisVerticalOutline />
                  </n-icon>
                </template>
              </n-button>
            </n-dropdown>
          </div>
        </div>

        <!-- Second Row: Summary -->
        <div class="mb-2 text-left">
          <h3 class="text-sm text-gray-900 dark:text-dark-text truncate leading-tight">
            {{ ticket.summary }}
          </h3>
        </div>
        
        <!-- Third Row: Preview -->
        <div class="text-left">
          <div class="text-xs text-gray-600 dark:text-dark-text-secondary truncate">
            {{ getTicketPreview(ticket) }}
          </div>
        </div>
      </div>
    </div>

    <!-- Log Work Modal -->
    <n-modal v-model:show="showLogModal" preset="card" title="Add Work Log" style="width: 500px;">
      <n-form>
        <div class="mb-4">
          <div class="text-sm text-gray-600 dark:text-dark-text-secondary mb-2">
            Ticket: <span class="font-medium text-blue-600 dark:text-dark-accent">{{ currentLogTicket?.issues_key }}</span>
          </div>
          <div class="text-sm text-gray-800 dark:text-dark-text font-medium">
            {{ currentLogTicket?.summary }}
          </div>
        </div>

        <n-form-item label="Time Spent (hours)" required>
          <n-input-number
            v-model:value="logFormData.worklog_timeSpent"
            :min="0.1"
            :step="0.25"
            :precision="2"
            placeholder="Enter time spent"
            style="width: 100%"
          />
        </n-form-item>

        <n-form-item label="Work Comment">
          <n-input
            v-model:value="logFormData.worklog_comment"
            type="textarea"
            placeholder="Describe the work performed..."
            :autosize="{ minRows: 3, maxRows: 6 }"
          />
        </n-form-item>
      </n-form>

      <template #footer>
        <div class="flex justify-end space-x-3">
          <n-button @click="closeLogModal" :disabled="logFormLoading">
            Cancel
          </n-button>
          <n-button
            type="primary"
            @click="saveWorklog"
            :loading="logFormLoading"
            :disabled="!logFormData.worklog_timeSpent || logFormData.worklog_timeSpent <= 0"
          >
            Save
          </n-button>
        </div>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { computed, watch, ref, reactive, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { NSpin, NIcon, NInput, NDropdown, NButton, useMessage, NModal, NForm, NFormItem, NInputNumber, NTooltip } from 'naive-ui';
import { 
  DocumentsOutline,
  FlameOutline,
  WarningOutline,
  InformationCircleOutline,
  RemoveCircleOutline,
  CheckmarkCircleOutline,
  TimeOutline,
  PlayCircleOutline,
  StopCircleOutline,
  SearchOutline,
  EllipsisVerticalOutline
} from '@vicons/ionicons5';
import { storeToRefs } from 'pinia';
import { useTicketStore } from '@/stores/ticket';
import { useUserStore } from '@/stores/user';
import { format } from 'date-fns';
import type { Ticket, Worklog } from '@/types/ticket';
import { ticketAPI } from '@/api/ticket';

const route = useRoute();
const router = useRouter();
const ticketStore = useTicketStore();
const userStore = useUserStore();
const message = useMessage();

// 从store获取响应式引用
const { allTickets, createdTickets, assignedTickets, loading, currentFilter, currentTicket } = storeToRefs(ticketStore);

// Log模态对话框状态
const showLogModal = ref(false);
const logFormLoading = ref(false);
const currentLogTicket = ref<Ticket | null>(null);

// Log表单数据
const logFormData = reactive({
  worklog_timeSpent: 0,
  worklog_comment: ''
});

const selectedTicketId = ref<string>('');
const searchQuery = ref<string>('');
const showSearchBox = ref<boolean>(false);
const searchInputRef = ref();

// 状态过滤相关
const selectedStatuses = ref<string[]>(['Open', 'In Progress', 'Resolved', 'Closed']); // 默认选择所有状态

// 可用状态配置
const availableStatuses = [
  {
    value: 'Open',
    label: 'Open',
    color: '#3b82f6',
    icon: TimeOutline
  },
  {
    value: 'In Progress',
    label: 'In Progress',
    color: '#f59e0b',
    icon: PlayCircleOutline
  },
  {
    value: 'Resolved',
    label: 'Resolved',
    color: '#10b981',
    icon: CheckmarkCircleOutline
  },
  {
    value: 'Closed',
    label: 'Closed',
    color: '#6b7280',
    icon: StopCircleOutline
  }
];

// 修复标题显示问题：基于 store 中的 currentFilter 而不是路由参数
const currentTitle = computed(() => {
  // 优先使用 store 中的 currentFilter，如果没有则使用路由参数
  const filter = currentFilter.value || route.params.filter as string;
  return filter === 'created' ? 'Created by Me' : 'Assigned to Me';
});

// 获取当前视图的票据
const currentTickets = computed(() => {
  return currentFilter.value === 'created' ? createdTickets.value : assignedTickets.value;
});

// 过滤工单：根据搜索查询和状态过滤 issues_key 和 summary
const filteredTickets = computed(() => {
  if (!currentTickets.value) return [];

  let filtered = currentTickets.value;

  // 状态过滤 - 修复逻辑：只有当有选中状态时才进行过滤
  if (selectedStatuses.value.length > 0) {
    filtered = filtered.filter(ticket => selectedStatuses.value.includes(ticket.status));
  } else {
    // 如果没有选中任何状态，返回空数组
    filtered = [];
  }

  // 搜索过滤
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase().trim();
    filtered = filtered.filter(ticket =>
      ticket.issues_key.toLowerCase().includes(query) ||
      ticket.summary.toLowerCase().includes(query)
    );
  }

  return filtered;
});

// 获取菜单选项
const getMenuOptions = (ticket: Ticket) => {
  const baseOptions = [
    {
      label: 'Start',
      key: `start-${ticket.issues_id}`,
      disabled: ticket.status === 'In Progress' || ticket.status === 'Resolved' || ticket.status === 'Closed'
    },
    {
      label: 'Suspend',
      key: `suspend-${ticket.issues_id}`,
      disabled: ticket.status !== 'In Progress'
    },
    {
      label: 'Close',
      key: `close-${ticket.issues_id}`,
      disabled: ticket.status === 'Closed'
    },
    {
      type: 'divider'
    },
    {
      label: 'Log',
      key: `log-${ticket.issues_id}`
    }
  ];
  
  return baseOptions;
};

// 处理菜单选择
const handleMenuSelect = (key: string) => {
  // 只分割第一个连字符，因为ticketId可能包含连字符
  const dashIndex = key.indexOf('-');
  const action = key.substring(0, dashIndex);
  const ticketId = key.substring(dashIndex + 1);

  // 从所有票据中查找，而不是只从当前视图的票据中查找
  const ticket = allTickets.value?.find(t => t.issues_id === ticketId);

  if (!ticket) {
    console.error('Ticket not found:', ticketId);
    return;
  }

  switch (action) {
    case 'start':
      message.info(`Start processing ticket: ${ticket.issues_key}`);
      // TODO: Implement start processing logic
      break;
    case 'suspend':
      message.info(`Suspend ticket: ${ticket.issues_key}`);
      // TODO: Implement suspend logic
      break;
    case 'close':
      message.info(`Close ticket: ${ticket.issues_key}`);
      // TODO: Implement close logic
      break;
    case 'log':
      openLogModal(ticket);
      break;
  }
};

// 打开Log模态对话框
const openLogModal = (ticket: Ticket) => {
  currentLogTicket.value = ticket;
  logFormData.worklog_timeSpent = 0;
  logFormData.worklog_comment = '';
  showLogModal.value = true;
};

// 关闭Log模态对话框
const closeLogModal = () => {
  showLogModal.value = false;
  currentLogTicket.value = null;
  logFormData.worklog_timeSpent = 0;
  logFormData.worklog_comment = '';
};

// 保存工作日志
const saveWorklog = async () => {
  if (!currentLogTicket.value) return;

  // 验证必填字段
  if (!logFormData.worklog_timeSpent || logFormData.worklog_timeSpent <= 0) {
    message.error('Time spent must be greater than 0');
    return;
  }

  try {
    logFormLoading.value = true;

    const worklogData: Partial<Worklog> = {
      worklog_author: userStore.getCurrentUserEmail(),
      worklog_time: new Date().toISOString(),
      worklog_timeSpent: logFormData.worklog_timeSpent,
      worklog_comment: logFormData.worklog_comment,
      ticket_id: currentLogTicket.value.issues_id
    };

    // 调用API添加worklog
    const apiResponse = await ticketAPI.addWorklogToTicket(currentLogTicket.value.issues_id, worklogData);

    // 手动将新的worklog添加到现有票据中
    const currentTicketCopy = { ...currentLogTicket.value };
    if (!currentTicketCopy.worklogs) {
      currentTicketCopy.worklogs = [];
    }

    // 添加新的worklog（从API响应中获取）
    if (apiResponse.worklogs && apiResponse.worklogs.length > 0) {
      currentTicketCopy.worklogs.push(...apiResponse.worklogs);
    }

    // 更新票据的updated时间
    currentTicketCopy.updated = apiResponse.updated || new Date().toISOString();

    // 直接更新store中的票据数据，避免重新加载
    ticketStore.updateTicketInStore(currentTicketCopy);

    message.success(`Work log added to ticket ${currentLogTicket.value.issues_key}`);
    closeLogModal();
  } catch (error) {
    console.error('Error adding worklog:', error);
    message.error('Failed to add work log');
  } finally {
    logFormLoading.value = false;
  }
};

const selectTicket = (ticket: Ticket) => {
  selectedTicketId.value = ticket.issues_id;
  // 使用store中的方法设置当前票据，避免重新加载
  ticketStore.setCurrentTicket(ticket.issues_id);
  // 跳转到详情页
  router.push(`/ticket/${ticket.issues_id}`);
};

// Priority icon mapping
const getPriorityIcon = (priority: string) => {
  const iconMap: Record<string, any> = {
    'Critical': FlameOutline,
    'High': WarningOutline,
    'Medium': InformationCircleOutline,
    'Low': RemoveCircleOutline,
    'Minor': RemoveCircleOutline
  };
  return iconMap[priority] || InformationCircleOutline;
};

// Priority color mapping
const getPriorityColor = (priority: string) => {
  const colorMap: Record<string, string> = {
    'Critical': '#ef4444',
    'High': '#f97316',
    'Medium': '#3b82f6',
    'Low': '#10b981',
    'Minor': '#9ca3af'
  };
  return colorMap[priority] || '#6b7280';
};

// Status icon mapping
const getStatusIcon = (status: string) => {
  const iconMap: Record<string, any> = {
    'Open': TimeOutline,
    'In Progress': PlayCircleOutline,
    'Resolved': CheckmarkCircleOutline,
    'Closed': StopCircleOutline
  };
  return iconMap[status] || TimeOutline;
};

// Status color mapping
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    'Open': '#3b82f6',
    'In Progress': '#f59e0b',
    'Resolved': '#10b981',
    'Closed': '#6b7280'
  };
  return colorMap[status] || '#6b7280';
};

const formatDate = (dateString: string) => {
  return format(new Date(dateString), 'MM/dd HH:mm');
};

// 搜索框显示控制
const toggleSearchBox = async () => {
  showSearchBox.value = !showSearchBox.value;
  // 如果隐藏搜索框，清空搜索内容
  if (!showSearchBox.value) {
    searchQuery.value = '';
  } else {
    // 如果显示搜索框，等待DOM更新后自动获得焦点
    await nextTick();
    searchInputRef.value?.focus();
  }
};

// 状态过滤方法
const toggleStatusFilter = (status: string) => {
  const index = selectedStatuses.value.indexOf(status);
  if (index > -1) {
    selectedStatuses.value.splice(index, 1);
  } else {
    selectedStatuses.value.push(status);
  }
};



const getTicketPreview = (ticket: Ticket) => {
  // Extract preview text from description
  const desc = ticket.description;
  if (desc.includes('|')) {
    const parts = desc.split('|');
    const lastPart = parts[parts.length - 2] || parts[parts.length - 1];
    return lastPart.length > 50 ? lastPart.substring(0, 50) + '...' : lastPart;
  }
  return desc.length > 50 ? desc.substring(0, 50) + '...' : desc;
};

// Watch route changes to load tickets and set current filter
watch(() => route.params.filter, (filter) => {
  if (filter && typeof filter === 'string') {
    console.log('Loading tickets for filter:', filter);
    // 设置当前过滤器
    ticketStore.currentFilter = filter as 'created' | 'assigned';
    // 加载所有票据（如果还没有加载）
    ticketStore.loadTickets();
    // 清除选中状态和搜索查询当切换工单类型时
    selectedTicketId.value = '';
    searchQuery.value = '';
  }
}, { immediate: true });

// Watch route changes to clear selection when switching between ticket types
watch(() => route.path, () => {
  if (route.name === 'tickets') {
    selectedTicketId.value = '';
  }
});

// Watch current ticket to update selection
watch(() => currentTicket.value, (newCurrentTicket) => {
  if (newCurrentTicket && route.name === 'ticket-detail') {
    selectedTicketId.value = newCurrentTicket.issues_id;
  } else if (route.name === 'tickets') {
    selectedTicketId.value = '';
  }
}, { immediate: true });

// Debug tickets
watch(currentTickets, (newTickets) => {
  console.log('Current tickets updated in component:', newTickets?.length || 0);
}, { immediate: true });
</script>