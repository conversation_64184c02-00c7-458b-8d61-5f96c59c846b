import { defineStore } from "pinia";
import { reactive, ref, type Ref, Reactive } from "vue";
import {
  priorityOptions,
  componentsOptions,
  servicePackageOptions,
} from "@/utils/mock";
import { RecordOptionKey, ItemOptionKey, ItemOptionValue, OPTIONS_KEYS, FetchFnType } from "@/types/form";


const getPriority = async () => {
  await new Promise((res) => setTimeout(res, 1000));
  return priorityOptions;
};

const getComponents = async () => {
  await new Promise((res) => setTimeout(res, 1000));
  return componentsOptions;
};

const getServicePackage = async () => {
  await new Promise((res) => setTimeout(res, 1000));
  return servicePackageOptions;
};

/**
 * useOptionsStore
 *
 * 统一管理下拉选项相关数据的 Pinia 状态管理方法。
 * 支持通过 key 动态获取、加载和扩展选项，兼容已存在和未存在的 key。
 *
 * 功能说明：
 * - loadOptions(key): 异步加载指定 key 的选项，自动处理 loading 状态，支持动态新增 key。
 * - getOptions(key): 获取指定 key 的选项数组，未加载时返回空数组，保持响应式。
 * - getLoading(key): 获取指定 key 的加载状态，响应式。
 * - 支持动态扩展 options，便于灵活管理不同下拉选项。
 */
export const useOptionsStore = defineStore("options", () => {
  const _options = new Map<RecordOptionKey, Reactive<ItemOptionValue>>();

  const initOptions = () => {
    const fetchFnObj: Record<ItemOptionKey, FetchFnType> = {
      [OPTIONS_KEYS.PRIORITY]: getPriority,
      [OPTIONS_KEYS.COMPONENTS]: getComponents,
      [OPTIONS_KEYS.SERVICE_PACKAGE]: getServicePackage,
    };

    Object.values(OPTIONS_KEYS).forEach((k) => {
      _options.set(
        k,
        reactive({
          loading: false,
          fetchFn: fetchFnObj[k],
          options: [],
        })
      );
    });
  };

  initOptions();

  const setOptions = (key: RecordOptionKey) => {
    _options.set(
      key,
      reactive({
        loading: false,
        fetchFn: async () => {
          // key = 接口地址，去请求
          return [];
        },
        options: [],
      })
    );
  };

  const hasOptions = (key: RecordOptionKey) => {
    if (!_options.has(key)) {
      setOptions(key);
    }
  };

  const loadOptions = async (key: RecordOptionKey, force = false) => {
    hasOptions(key);

    const item = _options.get(key)!;
    
    if (item.loading || (!force && item.options.length)) return;

    try {
      item.loading = true;
      item.options = item.options.concat(await item.fetchFn());
    } finally {
      item.loading = false;
    }
  };

  const getOptions = (key: RecordOptionKey) => {
    hasOptions(key);

    const item = _options.get(key)!;
    return item.options;
  };

  const getLoading = (key: RecordOptionKey) => {
    hasOptions(key);

    const item = _options.get(key)!;
    return item.loading;
  };

  return {
    loadOptions,
    getOptions,
    getLoading,
  };
});
