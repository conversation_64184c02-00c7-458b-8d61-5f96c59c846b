import { defineStore } from 'pinia';
import { ref } from 'vue';
import type { ChatMessage } from '@/types/ChatMessage';
import type { ClientMessage } from '@/types/protocol';
import { ChatSSEService, type SSEEventHandlers } from '@/api/chatSSE';
import { useUIStore } from '@/stores/ui';

export const useChatStore = defineStore('chat', () => {
  const messages = ref<ChatMessage[]>([]);
  const isLoading = ref(false);
  const isStreaming = ref(false);
  const currentAbortController = ref<AbortController | null>(null);
  const uiStore = useUIStore();
  // 内部方法：取消当前正在处理的消息
  // 格式化命令payload为可读的字符串
  const formatCommandPayload = (command: string, payload: any): string => {
    // 使用 JSON.stringify 的第三个参数进行格式化
    const jsonString = JSON.stringify(payload, null, 2);

    // 移除外层的大括号并清理格式
    const cleanedJson = jsonString
      .replace(/^\{\s*\n/, '')  // 移除开头的 {
      .replace(/\n\s*\}$/, '')  // 移除结尾的 }
      .replace(/^\s*"([^"]+)":\s*/gm, '• $1: ')  // 将 "key": 转换为 • key:
      .replace(/,\s*\n/g, '\n')  // 移除逗号
      .replace(/"/g, '')  // 移除引号
      .replace(/\btrue\b/g, 'Yes')  // 布尔值转换
      .replace(/\bfalse\b/g, 'No')
      .replace(/\bnull\b/g, 'N/A')
      .replace(/\bundefined\b/g, 'N/A');

    return cleanedJson || '• No parameters';
  };

  const cancelProcessing = (updateMessageStatus = true) => {
    if (currentAbortController.value) {
      currentAbortController.value.abort();

      if (updateMessageStatus) {
        // 找到最后一个正在处理的消息并标记为取消
        const lastProcessingMessage = messages.value
          .slice()
          .reverse()
          .find(msg => msg.status === 'loading');

        if (lastProcessingMessage) {
          lastProcessingMessage.status = 'cancelled';
          if (lastProcessingMessage.type === 'system') {
            lastProcessingMessage.content = lastProcessingMessage.content.replace('执行中', '已取消');
          }
          // 对于 assistant 消息，不在内容中添加取消标记，由UI状态标识显示
          // 强制触发响应式更新
          messages.value = [...messages.value];
        }
      }

      // 重置状态
      isStreaming.value = false;
      isLoading.value = false;
      currentAbortController.value = null;
    }
  };

  const sendMessage = async (content: string) => {
    try {
      // 如果有正在进行的请求，先取消它
      cancelProcessing();

      // 创建新的取消控制器
      currentAbortController.value = ChatSSEService.createAbortController();

      // 创建用户消息
      const userMessage: ChatMessage = {
        id: Date.now().toString(),
        content,
        type: 'user',
        timestamp: new Date()
      };

      // 立即添加用户消息到列表
      messages.value.push(userMessage);

      // 设置加载状态
      isLoading.value = true;

      // 创建助手消息占位符
      const assistantMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: '',
        type: 'assistant',
        timestamp: new Date(),
        status: 'loading'
      };

      // 添加空的助手消息到列表
      messages.value.push(assistantMessage);

      // 开始流式输出
      isStreaming.value = true;
      isLoading.value = false;

      // 构建聊天消息
      const clientMessage: ClientMessage = {
        messageId: assistantMessage.id,
        type: 'chat',
        language: uiStore.language, 
        payload: { content }
      };

      // 定义SSE事件处理器
      const handlers: SSEEventHandlers = {
        onTokenChunk: (data) => {
          // 累积内容
          assistantMessage.content += data.content;
          // 强制触发响应式更新
          messages.value = [...messages.value];
        },
        onStreamEnd: () => {
          assistantMessage.status = 'done';
          isStreaming.value = false;
          currentAbortController.value = null;
          // 强制触发响应式更新
          messages.value = [...messages.value];
        },
        onError: (data) => {
          if (data.message !== 'Request aborted') {
            if (import.meta.env.DEV) {
              console.error('SSE Error:', data);
            }
            // 保留已有内容，追加错误信息而不是替换
            if (assistantMessage.content.trim()) {
              assistantMessage.content += '\n\n❌ Sorry, an error occurred while processing your request. Please try again later.';
            } else {
              assistantMessage.content = 'Sorry, an error occurred while processing your request. Please try again later.';
            }
            assistantMessage.status = 'error';
          } else {
            // 命令被中止，标记为取消状态
            if (import.meta.env.DEV) {
              console.log('SSE stopped by user');
            }
            assistantMessage.status = 'cancelled';
            // 对于流式消息，不在内容中添加取消标记，由UI状态标识显示
            isStreaming.value = false;
          }
          // 清理控制器
          currentAbortController.value = null;
          // 强制触发响应式更新
          messages.value = [...messages.value];
        }
      };

      // 使用新的SSE服务
      await ChatSSEService.sendMessage(clientMessage, handlers, {
        abortSignal: currentAbortController.value.signal
      });

    } catch (error) {
      if (import.meta.env.DEV || import.meta.env.VITE_USE_MOCK_DATA === 'true') {
        console.error('Error sending message:', error);
      }
      // 命令执行异常，清理控制器和流式状态
      currentAbortController.value = null;
      isStreaming.value = false;
    } finally {
      isLoading.value = false;
    }
  };

  const sendCommand = async (command: string, payload: any, displayMessage?: string) => {
    try {
      // 如果有正在进行的请求，先取消它
      cancelProcessing();

      // 创建新的取消控制器
      currentAbortController.value = ChatSSEService.createAbortController();

      // 判断是否为流式命令
      const isStreamingCommand = ['query_device', 'plan_work'].includes(command);

      // 格式化payload信息
      const payloadInfo = formatCommandPayload(command, payload);
      const commandName = displayMessage || `${command}`;
      const commandContent = `${commandName}\n📋 Command Parameters:\n${payloadInfo}\n\n`;

      // 为流式命令创建 assistant 消息，为非流式命令创建 system 消息
      const commandMessage: ChatMessage = {
        id: Date.now().toString(),
        content: commandContent,
        type: isStreamingCommand ? 'assistant' : 'system',
        timestamp: new Date(),
        status: 'loading',
        metadata: {
          commandType: command,
          requestId: Date.now().toString(),
          isStreaming: isStreamingCommand
        }
      };

      // 添加命令消息到列表
      messages.value.push(commandMessage);

      // 流式命令设置流式状态
      if (isStreamingCommand) {
        isStreaming.value = true;
      }

      // 构建命令消息
      const clientMessage: ClientMessage = {
        messageId: commandMessage.metadata!.requestId!,
        type: 'command',
        language: uiStore.language, 
        command: command as any, // 类型断言，实际使用时需要确保command是有效的
        payload
      };

      // 定义SSE事件处理器
      const handlers: SSEEventHandlers = {
        onTokenChunk: (data) => {
          // 只有流式命令才处理 token_chunk
          if (commandMessage.metadata?.isStreaming) {
            // 累积内容到 assistant 消息
            commandMessage.content += data.content;
            // 强制触发响应式更新
            messages.value = [...messages.value];
          }
        },
        onCommandReceipt: (data) => {
          if (commandMessage.metadata?.isStreaming) {
            // 流式命令的 command_receipt 只用于状态更新，不修改内容
            commandMessage.status = data.status === 'success' ? 'done' : 'error';
          } else {
            // 非流式命令：追加结果消息，不覆盖现有内容
            if (data.status === 'success') {
              commandMessage.content += `\nSucceed: ${data.message}`;
              commandMessage.status = 'done';
            } else {
              commandMessage.content += `\nFailed: ${data.message}`;
              commandMessage.status = 'error';
            }
          }
          // 强制触发响应式更新
          messages.value = [...messages.value];
        },
        onStreamEnd: () => {
          if (commandMessage.metadata?.isStreaming) {
            commandMessage.status = 'done';
            isStreaming.value = false;
          }
          // 命令完成，清理控制器
          currentAbortController.value = null;
          // 强制触发响应式更新
          messages.value = [...messages.value];
        },
        onError: (data) => {
          if (data.message !== 'Request aborted') {
            if (import.meta.env.DEV) {
              console.error('Command Error:', data);
            }
            if (commandMessage.metadata?.isStreaming) {
              commandMessage.content += `\n命令执行出错: ${data.message}`;
            } else {
              commandMessage.content = `[命令错误: ${data.message}]`;
            }
            commandMessage.status = 'error';
          } else {
            // 命令被中止，标记为取消状态
            if (import.meta.env.DEV) {
              console.log('Command stopped by user');
            }
            commandMessage.status = 'cancelled';
            if (commandMessage.metadata?.isStreaming) {
              // 对于流式命令，不在内容中添加取消标记，由UI状态标识显示
              isStreaming.value = false;
            } else {
              commandMessage.content = commandMessage.content.replace('执行中', '已取消');
            }
          }
          // 命令出错，清理控制器
          currentAbortController.value = null;
          // 强制触发响应式更新
          messages.value = [...messages.value];
        }
      };

      // 使用新的SSE服务发送命令
      await ChatSSEService.sendMessage(clientMessage, handlers, {
        abortSignal: currentAbortController.value.signal
      });

    } catch (error) {
      if (import.meta.env.DEV || import.meta.env.VITE_USE_MOCK_DATA === 'true') {
        console.error('Error sending command:', error);
      }
      // 命令执行异常，清理控制器和流式状态
      currentAbortController.value = null;
      isStreaming.value = false;
    }
  };

  const clearMessages = () => {
    try {
      // 取消当前处理但不更新消息状态（因为要清空所有消息）
      cancelProcessing(false);

      // 清空消息列表
      messages.value = [];
    } catch (error) {
      if (import.meta.env.DEV || import.meta.env.VITE_USE_MOCK_DATA === 'true') {
        console.error('Error clearing messages:', error);
      }
    }
  };

  const stopStreaming = () => {
    try {
      cancelProcessing();
    } catch (error) {
      if (import.meta.env.DEV || import.meta.env.VITE_USE_MOCK_DATA === 'true') {
        console.error('Error stopping streaming:', error);
      }
    }
  };

  return {
    messages,
    isLoading,
    isStreaming,
    sendMessage,
    sendCommand,
    clearMessages,
    stopStreaming
  };
});