import { defineStore } from 'pinia';
import { ref, computed, watch } from 'vue';
import type { Ticket } from '@/types/ticket';
import { useUserStore } from '@/stores/user';
import { useUIStore } from '@/stores/ui';
import { ticketAPI } from '@/api/ticket';
import { tr } from 'date-fns/locale';

export const useTicketStore = defineStore('ticket', () => {
  // 统一的票据数据存储 - 所有票据都存储在这里
  const allTickets = ref<Ticket[]>([]);
  const currentTicket = ref<Ticket | null>(null);
  const loading = ref(false);
  const currentFilter = ref<'created' | 'assigned' | null>(null);
  
  // 定时刷新相关状态
  const refreshTimer = ref<NodeJS.Timeout | null>(null);
  const refreshing = ref(false);

  // 获取用户存储实例
  const userStore = useUserStore();
  const uiStore = useUIStore();

  // 自动刷新管理方法
  const startAutoRefresh = () => {
    if (refreshTimer.value) {
      clearInterval(refreshTimer.value);
    }
    
    const intervalMs = uiStore.ticket_refresh_interval * 60 * 1000;
    
    refreshTimer.value = setInterval(async () => {
      if (!uiStore.autoRefreshEnabled || refreshing.value) {
        return;
      }
      
      try {
        refreshing.value = true;
        const userEmail = userStore.getCurrentUserEmail();
        
        if (import.meta.env.DEV) {
          console.log('[Auto Refresh] Starting automatic ticket refresh...');
        }
        
        await loadTicketsFromAPI(userEmail);
      } catch (error) {
        if (import.meta.env.DEV) {
          console.error('[Auto Refresh] Failed:', error);
        }
      } finally {
        refreshing.value = false;
      }
    }, intervalMs);
    
    if (import.meta.env.DEV) {
      console.log(`[Auto Refresh] Started with interval: ${uiStore.ticket_refresh_interval} minutes`);
    }
  };
  
  const stopAutoRefresh = () => {
    if (refreshTimer.value) {
      clearInterval(refreshTimer.value);
      refreshTimer.value = null;
      
      if (import.meta.env.DEV) {
        console.log('[Auto Refresh] Stopped');
      }
    }
  };

  // Watch for config changes to handle auto refresh and cache clearing
  watch(() => uiStore.autoRefreshEnabled, (enabled) => {
    if (enabled) {
      startAutoRefresh();
    } else {
      stopAutoRefresh();
    }
    
    if (import.meta.env.DEV) {
      console.log('[Auto Refresh] Toggled:', enabled ? 'enabled' : 'disabled');
    }
  });

  watch(() => uiStore.ticket_history_days, (newDays, oldDays) => {
    if (oldDays !== undefined && newDays !== oldDays) {
      // Clear cache when history days changed
      allTickets.value = [];
      currentTicket.value = null;
      currentFilter.value = null;
      uiStore.updateTicketConfig({
        ticket_refresh_from: null
      });
      loadTickets(true); // Force a refresh
      
      if (import.meta.env.DEV) {
        console.log(`[Cache Clear] ticket_history_days changed from ${oldDays} to ${newDays}, cache cleared`);
      }
    }
  });

  // 计算属性：根据当前用户邮箱过滤创建的票据，按updated时间倒序排列
  const createdTickets = computed(() => {
    const userEmail = userStore.getCurrentUserEmail();
    return allTickets.value
      .filter(ticket => ticket.reporter_email === userEmail)
      .sort((a, b) => new Date(b.updated).getTime() - new Date(a.updated).getTime());
  });

  // 计算属性：根据当前用户邮箱过滤分配的票据，按updated时间倒序排列
  const assignedTickets = computed(() => {
    const userEmail = userStore.getCurrentUserEmail();
    return allTickets.value
      .filter(ticket => ticket.assignee_email === userEmail)
      .sort((a, b) => new Date(b.updated).getTime() - new Date(a.updated).getTime());
  });

  // 获取当前视图的票据（根据过滤条件）
  const getCurrentTickets = () => {
    return currentFilter.value === 'created' ? createdTickets.value : assignedTickets.value;
  };

  // 加载所有票据（一次性加载，不再区分created/assigned）
  const loadTickets = async (forceRefresh: boolean = false) => {
    try {
      loading.value = true;

      // 如果强制刷新，清空现有数据
      if (forceRefresh) {
        allTickets.value = [];
        // 重置刷新时间，强制从历史开始加载
        uiStore.updateTicketConfig({ ticket_refresh_from: null });
      }

      // 如果没有数据或强制刷新，从API加载
      if (allTickets.value.length === 0 || forceRefresh) {
        const userEmail = userStore.getCurrentUserEmail();
        await loadTicketsFromAPI(userEmail);
        
        // 首次加载成功后检查并启动自动刷新
        if (uiStore.autoRefreshEnabled && !refreshTimer.value) {
          startAutoRefresh();
        }
      }
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error('Error loading tickets:', error);
      }
    } finally {
      loading.value = false;
    }
  };

  // Get ticket with specified ID from all tickets
  const getTicketById = (id: string): Ticket | null => {
    return allTickets.value.find((ticket: Ticket) => ticket.issues_id === id) || null;
  };

  // Set current ticket (从所有票据中查找)
  const setCurrentTicket = async (id: string) => {
    try {
      // 首先在现有的统一存储中查找
      let ticket = getTicketById(id);

      if (!ticket) {
        // 如果没找到，从API获取完整数据并搜索
        loading.value = true;
        const userEmail = userStore.getCurrentUserEmail();
        await loadTicketsFromAPI(userEmail);

        // 再次在更新后的存储中查找
        ticket = getTicketById(id);
      }

      if (ticket) {
        currentTicket.value = ticket;
      } else {
        currentTicket.value = null;
        if (import.meta.env.DEV) {
          console.error('Ticket not found:', id);
        }
      }
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error('Error setting current ticket:', error);
      }
      currentTicket.value = null;
    } finally {
      loading.value = false;
    }
  };

  const createTicket = async (ticketData: Partial<Ticket>) => {
    loading.value = true;
    try {
      const userEmail = userStore.getCurrentUserEmail();

      // 设置创建者邮箱
      const ticketToCreate = {
        ...ticketData,
        reporter_email: userEmail
      };

      // 调用票据API创建票据
      const newTicket = await ticketAPI.createTicket(ticketToCreate);

      // 确保在创建工单后，如果当前没有加载"我创建的工单"列表，则加载它
      if (currentFilter.value !== 'created') {
        currentFilter.value = 'created';
        // 如果没有数据，则先加载初始数据
        if (createdTickets.value.length === 0) {
          await loadTicketsFromAPI(userEmail);
        }
      }

      // 将新工单添加到统一存储的顶部
      allTickets.value.unshift(newTicket);

      return newTicket;
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error('Error creating ticket:', error);
      }
      throw error;
    } finally {
      loading.value = false;
    }
  };



  // 内部方法：从API加载票据数据（增量更新逻辑优化）
  const loadTicketsFromAPI = async (userEmail: string) => {
    try {
      const time_now = new Date().toISOString();

      // 根据配置计算from参数
      let from: string;
      if (uiStore.ticket_refresh_from === null) {
        // 如果 ticket_refresh_from 是null，from 等于从time_now - ticket_history_days
        const historyDate = new Date();
        historyDate.setDate(historyDate.getDate() - uiStore.ticket_history_days);
        from = historyDate.toISOString();
      } else {
        // 如果 ticket_refresh_from 不是null，from的时间就是ticket_refresh_from
        from = uiStore.ticket_refresh_from;
      }

      if (import.meta.env.DEV) {
        console.log('Loading tickets from API with params:', {
          userEmail,
          from,
          ticket_history_days: uiStore.ticket_history_days,
          ticket_refresh_from: uiStore.ticket_refresh_from
        });
      }

      // 调用票据API获取该用户相关的所有票据
      const userTickets = await ticketAPI.getTicketsByUser(userEmail, from);

      // 增量更新逻辑优化 - 避免界面闪动
      await updateTicketsIncrementally(userTickets);

      // 如果成功了，将 ticket_refresh_from 设置为 time_now
      uiStore.updateTicketConfig({
        ticket_refresh_from: time_now
      });

      if (import.meta.env.DEV) {
        console.log('Successfully loaded tickets, updated ticket_refresh_from to:', time_now);
      }
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error('Error loading tickets from API:', error);
      }
      // 如果失败，ticket_refresh_from 不变
      throw error;
    }
  };

  // 增量更新票据数据的核心方法
  const updateTicketsIncrementally = async (newTickets: Ticket[]) => {
    if (!Array.isArray(newTickets) || newTickets.length === 0) {
      if (import.meta.env.DEV) {
        console.log('No new tickets to update');
      }
      return;
    }

    // 创建现有票据的映射表，提高查找效率
    const existingTicketsMap = new Map(allTickets.value.map(ticket => [ticket.issues_id, ticket]));
    
    let addedCount = 0;
    let updatedCount = 0;
    const updatedTickets = [...allTickets.value];

    for (const newTicket of newTickets) {
      const existingTicket = existingTicketsMap.get(newTicket.issues_id);
      
      if (!existingTicket) {
        // 情况1：远程存在，本地不存在 -> 新增
        updatedTickets.push(newTicket);
        addedCount++;
        
        if (import.meta.env.DEV) {
          console.log(`[Incremental Update] Added new ticket: ${newTicket.issues_key}`);
        }
      } else {
        // 情况2：远程存在，本地存在 -> 检查是否需要更新
        const shouldUpdate = shouldUpdateTicket(existingTicket, newTicket);
        
        if (shouldUpdate) {
          // 找到现有票据的索引并替换
          const index = updatedTickets.findIndex(t => t.issues_id === newTicket.issues_id);
          if (index !== -1) {
            updatedTickets[index] = newTicket;
            updatedCount++;
            
            // 如果更新的是当前查看的票据，也要更新currentTicket
            if (currentTicket.value?.issues_id === newTicket.issues_id) {
              currentTicket.value = newTicket;
            }
            
            if (import.meta.env.DEV) {
              console.log(`[Incremental Update] Updated existing ticket: ${newTicket.issues_key}`);
            }
          }
        }
      }
    }

    // 一次性更新所有票据，避免多次响应式更新造成的界面闪动
    if (addedCount > 0 || updatedCount > 0) {
      allTickets.value = updatedTickets;
      
      if (import.meta.env.DEV) {
        console.log(`[Incremental Update] Summary - Added: ${addedCount}, Updated: ${updatedCount}, Total: ${updatedTickets.length}`);
      }
    }
  };

  // 判断票据是否需要更新的辅助函数
  const shouldUpdateTicket = (existingTicket: Ticket, newTicket: Ticket): boolean => {
    // 比较更新时间
    const existingUpdated = new Date(existingTicket.updated).getTime();
    const newUpdated = new Date(newTicket.updated).getTime();
    
    if (newUpdated > existingUpdated) {
      return true;
    }
    
    // 比较状态变化
    if (existingTicket.status !== newTicket.status) {
      return true;
    }
    
    // 比较工作日志数量
    const existingWorklogsCount = existingTicket.worklogs?.length || 0;
    const newWorklogsCount = newTicket.worklogs?.length || 0;
    
    if (newWorklogsCount > existingWorklogsCount) {
      return true;
    }
    
    return false;
  };

  // 自动刷新切换方法
  const toggleAutoRefresh = (enabled?: boolean) => {
    uiStore.updateTicketConfig({
      autoRefreshEnabled: enabled !== undefined ? enabled : !uiStore.autoRefreshEnabled
    });
  };
  
  // 手动触发刷新
  const manualRefresh = async () => {
    if (refreshing.value) {
      if (import.meta.env.DEV) {
        console.log('[Manual Refresh] Already refreshing, skipping...');
      }
      return;
    }
    
    try {
      refreshing.value = true;
      const userEmail = userStore.getCurrentUserEmail();
      
      if (import.meta.env.DEV) {
        console.log('[Manual Refresh] Starting manual refresh...');
      }
      
      await loadTicketsFromAPI(userEmail);
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error('[Manual Refresh] Failed:', error);
      }
      throw error;
    } finally {
      refreshing.value = false;
    }
  };

  // 清除本地缓存
  const clearCache = () => {
    // 在开发环境或使用 mock 数据时都允许清除缓存
    if (import.meta.env.DEV || import.meta.env.VITE_USE_MOCK_DATA === 'true') {
      stopAutoRefresh();
      allTickets.value = [];
      currentTicket.value = null;
      currentFilter.value = null;
      uiStore.updateTicketConfig({
        ticket_refresh_from: null
      }); 
      console.log('Local cache cleared');
    }
  };
  
  // 组件卸载时清理定时器
  const cleanup = () => {
    stopAutoRefresh();
  };

  // 辅助方法：计算票据总工时
  const getTotalWorkTime = (ticket: Ticket): number => {
    if (!ticket.worklogs) return 0;
    return ticket.worklogs.reduce((total, worklog) =>
      total + (worklog.worklog_timeSpent || 0), 0);
  };

  // 辅助方法：获取票据最新的工作日志
  const getLatestWorklog = (ticket: Ticket) => {
    if (!ticket.worklogs || ticket.worklogs.length === 0) return null;
    return ticket.worklogs.reduce((latest, current) => {
      const latestTime = new Date(latest.worklog_time || 0).getTime();
      const currentTime = new Date(current.worklog_time || 0).getTime();
      return currentTime > latestTime ? current : latest;
    });
  };

  // 辅助方法：按时间排序工作日志
  const getSortedWorklogs = (ticket: Ticket, ascending: boolean = false) => {
    if (!ticket.worklogs) return [];
    return [...ticket.worklogs].sort((a, b) => {
      const timeA = new Date(a.worklog_time || 0).getTime();
      const timeB = new Date(b.worklog_time || 0).getTime();
      return ascending ? timeA - timeB : timeB - timeA;
    });
  };

  // 更新store中的特定票据
  const updateTicketInStore = (updatedTicket: Ticket) => {
    const index = allTickets.value.findIndex(t => t.issues_id === updatedTicket.issues_id);
    if (index !== -1) {
      allTickets.value[index] = updatedTicket;

      // 如果更新的是当前票据，也更新currentTicket
      if (currentTicket.value?.issues_id === updatedTicket.issues_id) {
        currentTicket.value = updatedTicket;
      }
    }
  };

  return {
    // 数据
    allTickets,
    createdTickets,
    assignedTickets,
    currentTicket,
    loading,
    currentFilter,
    getCurrentTickets,
    
    // 自动刷新相关状态
    refreshing,

    // 方法
    loadTickets,
    getTicketById,
    setCurrentTicket,
    createTicket,
    clearCache,
    
    // 自动刷新管理方法
    startAutoRefresh,
    stopAutoRefresh,
    toggleAutoRefresh,
    manualRefresh,
    cleanup,

    // 辅助方法
    getTotalWorkTime,
    getLatestWorklog,
    getSortedWorklogs,
    updateTicketInStore
  };
}, {
  persist: {
    key: 'iticket-store',
    storage: localStorage,
    paths: ['allTickets'] // 只持久化票据数据
  }
});
