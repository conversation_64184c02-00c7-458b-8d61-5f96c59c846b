import { defineStore } from 'pinia';
import { ref } from 'vue';

/**
 * 用户存储 - 管理当前登录用户信息
 * 为将来的认证系统集成做准备
 */
export const useUserStore = defineStore('user', () => {
  // 当前登录用户邮箱 - 在实际应用中这会来自认证系统
  const currentUserEmail = ref('<EMAIL>');
  
  // 用户认证状态
  const isAuthenticated = ref(true);
  
  // 用户基本信息（为将来扩展预留）
  const userInfo = ref({
    email: '<EMAIL>',
    name: 'Current User',
    department: 'Development',
    role: 'user'
  });
  
  /**
   * 获取当前用户邮箱
   */
  const getCurrentUserEmail = () => currentUserEmail.value;
  
  /**
   * 设置当前用户邮箱（用于测试或认证后设置）
   */
  const setCurrentUserEmail = (email: string) => {
    currentUserEmail.value = email;
    // 同时更新用户信息中的邮箱
    userInfo.value.email = email;
    
    if (import.meta.env.DEV) {
      console.log('User email updated:', email);
    }
  };
  
  /**
   * 设置用户信息（认证后调用）
   */
  const setUserInfo = (info: Partial<typeof userInfo.value>) => {
    userInfo.value = { ...userInfo.value, ...info };
    currentUserEmail.value = info.email || currentUserEmail.value;
    
    if (import.meta.env.DEV) {
      console.log('User info updated:', userInfo.value);
    }
  };
  
  /**
   * 设置认证状态
   */
  const setAuthenticated = (status: boolean) => {
    isAuthenticated.value = status;
    
    if (import.meta.env.DEV) {
      console.log('Authentication status updated:', status);
    }
  };
  
  /**
   * 用户登录（为将来的认证系统预留）
   */
  const login = async (credentials: { email: string; password: string }) => {
    try {
      // TODO: 实际的登录API调用
      // const response = await authAPI.login(credentials);
      
      // 模拟登录成功
      setUserInfo({
        email: credentials.email,
        name: credentials.email.split('@')[0],
        department: 'Development',
        role: 'user'
      });
      setAuthenticated(true);
      
      if (import.meta.env.DEV) {
        console.log('User logged in:', credentials.email);
      }
      
      return { success: true };
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error('Login failed:', error);
      }
      throw error;
    }
  };
  
  /**
   * 用户登出
   */
  const logout = async () => {
    try {
      // TODO: 实际的登出API调用
      // await authAPI.logout();
      
      // 清除用户信息
      currentUserEmail.value = '';
      userInfo.value = {
        email: '',
        name: '',
        department: '',
        role: 'user'
      };
      setAuthenticated(false);
      
      if (import.meta.env.DEV) {
        console.log('User logged out');
      }
      
      return { success: true };
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error('Logout failed:', error);
      }
      throw error;
    }
  };
  
  /**
   * 检查用户权限（为将来的权限系统预留）
   */
  const hasPermission = (permission: string): boolean => {
    // TODO: 实际的权限检查逻辑
    // 目前所有用户都有基本权限
    const basicPermissions = ['create_ticket', 'view_own_tickets', 'update_own_tickets'];
    return basicPermissions.includes(permission);
  };
  
  /**
   * 初始化用户状态（应用启动时调用）
   */
  const initializeUser = async () => {
    try {
      // TODO: 从token或session恢复用户状态
      // const token = localStorage.getItem('auth_token');
      // if (token) {
      //   const userInfo = await authAPI.validateToken(token);
      //   setUserInfo(userInfo);
      //   setAuthenticated(true);
      // }
      
      if (import.meta.env.DEV) {
        console.log('User store initialized');
      }
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error('User initialization failed:', error);
      }
      // 初始化失败时清除认证状态
      setAuthenticated(false);
    }
  };
  
  return {
    // 状态
    currentUserEmail,
    isAuthenticated,
    userInfo,
    
    // 方法
    getCurrentUserEmail,
    setCurrentUserEmail,
    setUserInfo,
    setAuthenticated,
    login,
    logout,
    hasPermission,
    initializeUser
  };
}, {
  persist: {
    key: 'iticket-user-store',
    storage: localStorage,
    paths: ['currentUserEmail', 'userInfo', 'isAuthenticated']
  }
});
