import { reactive, type Ref, watch } from "vue";
import { FormRules } from 'naive-ui'
import type { FormItemValue, FormItemComponentProps } from '@/types/form'


export function useFormValue<T extends FormItemValue>(fields: Ref<T[]>, defaultFormValue: Record<string, any> = {}) {

    const formValue = reactive<Record<string, any>>({
        ...structuredClone(defaultFormValue)
    })
    const rules = reactive<FormRules>({})

    const init = () => {
        const fieldKeys = [...fields.value.map(item => item.modelKey), ...Object.keys(defaultFormValue)]

        // 删除已不存在的字段
        Object.keys(formValue).forEach(key => {
            if (!fieldKeys.includes(key)) {
                delete formValue[key]
            }
        })
        Object.keys(rules).forEach(key => {
            if (!fieldKeys.includes(key)) {
                delete rules[key]
            }
        })

        fields.value.forEach((item: FormItemValue) => {
            const { modelKey, modelValue, modelType, required, trigger = '', message = '' } = item

            if (!(modelKey in formValue)) {
                formValue[modelKey] = modelValue ?? ''
            }

            if (required) {
                rules[modelKey] = {
                    type: modelType ?? 'string',
                    required,
                    trigger,
                    message
                }
            } else {
                delete rules[modelKey]
            }
        })
    }

    const add = (key: string, list: FormItemValue[]) => {

        if (Array.isArray(formValue[key])) {
            const items: { [key: string]: any } = {}
            list.forEach((item: FormItemValue, index: number) => {
            const { modelKey, modelValue, modelType, required, trigger = '', message = '' } = item
                items[modelKey] = modelValue
                

                const ruleKey = `${key}[${formValue[key].length}].${modelKey}`
                if (required) {
                    rules[ruleKey] = {
                        type: modelType ?? 'string',
                        required,
                        trigger,
                        message
                    }
                } else {
                    delete rules[ruleKey]
                }
            })
            formValue[key].push(items)
        }
    }

    const remove = (key: string, index?: number) => {
        if (Array.isArray(formValue[key])) {
            if (index !== undefined) {
                const item = formValue[key].splice(index, 1)
                for (let i = 0; i < item.length; i++) {
                    const ruleKey = `${key}[${index}].${item[i]}`;

                    delete rules[ruleKey]
                }
            }
        }
    }

    const reset = () => {
        fields.value.forEach((item) => {
            formValue[item.modelKey] = item.modelValue
        })

        for (const key in defaultFormValue) {
            formValue[key] = defaultFormValue[key]
        }
    }

    watch(fields, () => {
        init()
    })

    init()

    return { formValue, rules, add, remove, reset }
}
